const express = require('express');
const cors = require('cors');
const path = require('path');

// Import PDF Generator
const SiNaPDFGenerator = require('./src/services/pdfGenerator');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static frontend files
app.use(express.static('public'));

// Root route - serve frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Backend server running',
    timestamp: new Date().toISOString()
  });
});

// PDF generation endpoint
app.post('/api/generate-pdf', async (req, res) => {
  try {
    console.log('📥 PDF generation request received');
    console.log('Form data fields:', Object.keys(req.body).length);
    console.log('beilageSonstigeText:', req.body.beilageSonstigeText);
    console.log('anlageStromkreisText:', req.body.anlageStromkreisText);
    
    // Initialize PDF generator
    const pdfGenerator = new SiNaPDFGenerator();
    
    // Generate PDF with form data
    const pdfBuffer = await pdfGenerator.generatePDF(req.body);
    
    // Set response headers for PDF download
    const filename = pdfGenerator.getDefaultFilename(req.body);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    // Send PDF buffer
    res.send(pdfBuffer);
    
    console.log(`✅ PDF generation completed: ${filename}`);
    
  } catch (error) {
    console.error('❌ PDF generation error:', error);
    res.status(500).json({ 
      error: 'PDF generation failed', 
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// SPA fallback middleware - must be after all API routes
app.use((req, res, next) => {
  // If no route matched and it's not an API call, serve index.html
  if (!req.path.startsWith('/api')) {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  } else {
    next();
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Backend server running on http://localhost:${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📄 PDF endpoint: http://localhost:${PORT}/api/generate-pdf`);
  console.log(`✅ Backend is working correctly on port ${PORT}!`);
});
{"version": 3, "file": "BrowsingContextProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/context/BrowsingContextProcessor.ts"], "names": [], "mappings": ";;;AAmBA,+DAOuC;AAQvC,MAAa,wBAAwB;IAC1B,iBAAiB,CAAY;IAC7B,uBAAuB,CAAyB;IAChD,aAAa,CAAe;IAC5B,mBAAmB,CAAqB;IAEjD,YACE,gBAA2B,EAC3B,sBAA8C,EAC9C,kBAAsC,EACtC,YAA0B;QAE1B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACjC,0BAAY,CAAC,eAAe,CAAC,UAAU,CAAC,cAAc,EACtD,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/C,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAyC;QAEzC,MAAM,cAAc,GAClB,MAAM,CAAC,IAAI,KAAK,SAAS;YACvB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE;YACpD,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7D,OAAO;YACL,QAAQ,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACjC,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,CAC5D;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CACV,MAAwC;QAExC,IAAI,gBAAiD,CAAC;QACtD,IAAI,WAAW,GAAG,SAAS,CAAC;QAC5B,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CACxD,MAAM,CAAC,gBAAgB,CACxB,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC1C,MAAM,IAAI,sCAAwB,CAChC,gDAAgD,CACjD,CAAC;YACJ,CAAC;YACD,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC;QAC7C,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACrC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACnC,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB;aAClD,cAAc,EAAE;aAChB,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;QAE5D,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB;gBACE,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;YACR;gBACE,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;QACV,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC7B,qEAAqE;YACrE,yDAAyD;YACzD,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,IAAI,MAA4C,CAAC;QAEjD,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,qBAAqB,EAAE;gBACvE,GAAG,EAAE,aAAa;gBAClB,SAAS;gBACT,gBAAgB,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;gBACrE,UAAU,EAAE,MAAM,CAAC,UAAU,KAAK,IAAI;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb;YACE,oKAAoK;YACnK,GAAa,CAAC,OAAO,CAAC,UAAU,CAC/B,wCAAwC,CACzC;gBACD,iKAAiK;gBAChK,GAAa,CAAC,OAAO,KAAK,kBAAkB,EAC7C,CAAC;gBACD,MAAM,IAAI,wCAA0B,CAClC,eAAe,WAAW,gBAAgB,CAC3C,CAAC;YACJ,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,iFAAiF;QACjF,WAAW;QACX,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAC/D,MAAM,CAAC,QAAQ,CAChB,CAAC;QACF,oEAAoE;QACpE,4EAA4E;QAC5E,0EAA0E;QAC1E,oDAAoD;QACpD,kEAAkE;QAClE,MAAM,OAAO,CAAC,eAAe,EAAE,CAAC;QAEhC,OAAO,EAAC,OAAO,EAAE,OAAO,CAAC,EAAE,EAAC,CAAC;IAC/B,CAAC;IAED,QAAQ,CACN,MAA0C;QAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,OAAO,OAAO,CAAC,QAAQ,CACrB,MAAM,CAAC,GAAG,EACV,MAAM,CAAC,IAAI,oDAAuC,CACnD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,MAAwC;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,OAAO,OAAO,CAAC,MAAM,CACnB,MAAM,CAAC,WAAW,IAAI,KAAK,EAC3B,MAAM,CAAC,IAAI,oDAAuC,CACnD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAA0C;QAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,sCAAwB,CAChC,uDAAuD,CACxD,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAAmD;QAEnD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,KAAK,CACT,MAAuC;QAEvC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAA6C;QAE7C,MAAM,wBAAwB,GAC5B,MAAM,IAAI,CAAC,mCAAmC,CAC5C,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,YAAY,CACpB,CAAC;QAEJ,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,MAAM,iBAAiB,GACrB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAEpD,8DAA8D;YAC9D,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBAC1C,iBAAiB,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;YAC/D,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAClC,iBAAiB,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,wBAAwB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CACvC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAC9D,CACF,CAAC;QAEF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mCAAmC,CACvC,iBAA0B,EAC1B,cAAyB;QAEzB,IAAI,iBAAiB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACpE,MAAM,IAAI,sCAAwB,CAChC,iDAAiD,CAClD,CAAC;QACJ,CAAC;QAED,IAAI,iBAAiB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACpE,MAAM,IAAI,sCAAwB,CAChC,iDAAiD,CAClD,CAAC;QACJ,CAAC;QAED,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,OAAO,GACX,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC7D,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBACjC,MAAM,IAAI,sCAAwB,CAChC,+DAA+D,CAChE,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED,uCAAuC;QACvC,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,cAAe,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,MAAM,aAAa,IAAI,cAAe,EAAE,CAAC;YAC5C,MAAM,wBAAwB,GAAG,IAAI,CAAC,uBAAuB;iBAC1D,mBAAmB,EAAE;iBACrB,MAAM,CACL,CAAC,eAAe,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,KAAK,aAAa,CACnE,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC;QAC3C,CAAC;QACD,oFAAoF;QACpF,sEAAsE;QACtE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAiD;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,sCAAwB,CAChC,+BAA+B,MAAM,CAAC,OAAO,EAAE,CAChD,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,sCAAwB,CAChC,+DAA+D,CAChE,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAAkD;QAElD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,oCAAoC;YACpC,mKAAmK;YACnK,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,kCAAoB,CAAC,sBAAsB,CAAC,CAAC;YACzD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAuC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,sCAAwB,CAChC,kCAAkC,OAAO,CAAC,EAAE,oBAAoB,CACjE,CAAC;QACJ,CAAC;QACD,iFAAiF;QACjF,MAAM,eAAe,GAAG,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC;QAC1D,IAAI,CAAC;YACH,MAAM,yBAAyB,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBAC9D,MAAM,kBAAkB,GAAG,CACzB,KAA8C,EAC9C,EAAE;oBACF,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;wBACtC,eAAe,CAAC,GAAG,CACjB,2BAA2B,EAC3B,kBAAkB,CACnB,CAAC;wBACF,OAAO,EAAE,CAAC;oBACZ,CAAC;gBACH,CAAC,CAAC;gBACF,eAAe,CAAC,EAAE,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACxB,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,MAAM,eAAe,CAAC,WAAW,CAAC,oBAAoB,EAAE;wBACtD,QAAQ,EAAE,MAAM,CAAC,OAAO;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,yEAAyE;gBACzE,oDAAoD;gBACpD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzC,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YACD,oEAAoE;YACpE,0EAA0E;YAC1E,iDAAiD;YACjD,MAAM,yBAAyB,CAAC;QAClC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,yDAAyD;YACzD,kDAAkD;YAClD,IACE,CAAC,CACC,KAAK,CAAC,IAAI,iDAAoC;gBAC9C,KAAK,CAAC,OAAO,KAAK,gCAAgC,CACnD,EACD,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAA6C;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,8BAA8B,CAC5B,SAA0C;QAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,MAAM,gBAAgB,GAAG;YACvB,OAAO;YACP,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW;SAClE,CAAC;QACF,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,0BAAY,CAAC,eAAe,CAAC,UAAU,CAAC,cAAc;gBAC9D,MAAM,EAAE,OAAO,CAAC,oBAAoB,EAAE;aACvC,EACD,OAAO,CAAC,EAAE,CACX,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF;AA9WD,4DA8WC"}
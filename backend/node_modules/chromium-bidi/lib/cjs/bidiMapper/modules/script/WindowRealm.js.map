{"version": 3, "file": "WindowRealm.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/script/WindowRealm.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAKH,+DAKuC;AAOvC,yCAAiC;AAEjC,+CAAyD;AAEzD,MAAa,WAAY,SAAQ,gBAAK;IAC3B,kBAAkB,CAAkC;IACpD,uBAAuB,CAAyB;IAChD,OAAO,CAAqB;IAErC,YACE,iBAAkD,EAClD,sBAA8C,EAC9C,SAAoB,EACpB,YAA0B,EAC1B,kBAAuD,EACvD,MAA4B,EAC5B,MAAc,EACd,OAAqB,EACrB,YAA0B,EAC1B,OAA2B;QAE3B,KAAK,CACH,SAAS,EACT,YAAY,EACZ,kBAAkB,EAClB,MAAM,EACN,MAAM,EACN,OAAO,EACP,YAAY,CACb,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,qBAAqB,CAAC,WAAmB;QACvC,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB;aACtD,cAAc,EAAE;aAChB,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;QAC1D,OAAO,oBAAoB,EAAE,EAAE,IAAI,SAAS,CAAC;IAC/C,CAAC;IAED,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACM,QAAQ;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,IAAa,0BAA0B;QACrC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAChC,CAAC;IAED,IAAa,SAAS;QACpB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAa,SAAS;QACpB,OAAO;YACL,GAAG,IAAI,CAAC,QAAQ;YAChB,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,OAAO,EAAE,IAAI,CAAC,kBAAkB;YAChC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;IAED,IAAa,MAAM;QACjB,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,OAAO;YACnB,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE;SACjC,CAAC;IACJ,CAAC;IAEQ,gBAAgB,CACvB,mBAAyD,EACzD,aAAkC;QAElC,MAAM,SAAS,GAAG,mBAAmB,CAAC,KAAK,CAAC;QAC5C,IAAI,mBAAmB,CAAC,IAAI,KAAK,MAAM,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACnE,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,CAAC;gBAC9C,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,SAAS,CAAC;gBAChE,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC;oBACzC,4DAA4D;oBAC5D,8CAA8C;oBAC9C,yCAAyC;oBACzC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC;oBACjC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC/B,CAAC;gBACA,mBAAyD,CAAC,QAAQ;oBACjE,IAAA,yBAAW,EACT,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EACvC,WAAW,EACX,SAAS,CAAC,aAAa,CACxB,CAAC;gBACJ,OAAO,SAAS,CAAC,eAAe,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC;gBACzC,KAAK,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;oBACnC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAC3C,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EACrB,aAAa,CACd,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,IACE,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC;gBACtC,SAAS,CAAC,UAAU,KAAK,IAAI,EAC7B,CAAC;gBACD,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAC1C,SAAS,CAAC,UAAU,EACpB,aAAa,CACd,CAAC;YACJ,CAAC;YACD,8DAA8D;YAC9D,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,CAAC;gBAClC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;YAChC,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAEQ,KAAK,CAAC,iBAAiB,CAC9B,UAA6B;QAE7B,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,IAAA,2BAAa,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC1D,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;gBAC5B,MAAM,IAAI,iCAAmB,CAC3B,aAAa,UAAU,CAAC,QAAQ,kBAAkB,CACnD,CAAC;YACJ,CAAC;YACD,MAAM,EAAC,UAAU,EAAE,aAAa,EAAC,GAAG,cAAc,CAAC;YACnD,mFAAmF;YACnF,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;gBACpD,MAAM,IAAI,iCAAmB,CAC3B,aAAa,UAAU,CAAC,QAAQ,wDAAwD,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,CAC5H,CAAC;YACJ,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,EAAE;oBACnE,aAAa;oBACb,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;iBAC5C,CAAC,CAAC;gBACH,yDAAyD;gBACzD,OAAO,EAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,uEAAuE;gBACvE,sBAAsB;gBACtB,IACE,KAAK,CAAC,IAAI,iDAAoC;oBAC9C,KAAK,CAAC,OAAO,KAAK,6BAA6B,EAC/C,CAAC;oBACD,MAAM,IAAI,iCAAmB,CAC3B,aAAa,UAAU,CAAC,QAAQ,kBAAkB,CACnD,CAAC;gBACJ,CAAC;gBACD,MAAM,IAAI,mCAAqB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QACD,OAAO,MAAM,KAAK,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAEQ,KAAK,CAAC,QAAQ,CACrB,UAAkB,EAClB,YAAqB,EACrB,eAAuC,EACvC,oBAAiD,EACjD,cAAwB,EACxB,qBAA+B;QAE/B,MAAM,IAAI,CAAC,uBAAuB;aAC/B,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC;aACnC,sBAAsB,EAAE,CAAC;QAE5B,OAAO,MAAM,KAAK,CAAC,QAAQ,CACzB,UAAU,EACV,YAAY,EACZ,eAAe,EACf,oBAAoB,EACpB,cAAc,EACd,qBAAqB,CACtB,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,YAAY,CACzB,mBAA2B,EAC3B,YAAqB,EACrB,cAAiC,EACjC,oBAAyC,EACzC,eAAuC,EACvC,oBAAiD,EACjD,cAAwB;QAExB,MAAM,IAAI,CAAC,uBAAuB;aAC/B,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC;aACnC,sBAAsB,EAAE,CAAC;QAE5B,OAAO,MAAM,KAAK,CAAC,YAAY,CAC7B,mBAAmB,EACnB,YAAY,EACZ,cAAc,EACd,oBAAoB,EACpB,eAAe,EACf,oBAAoB,EACpB,cAAc,CACf,CAAC;IACJ,CAAC;CACF;AAnND,kCAmNC"}
{"name": "@babel/preset-react", "version": "7.27.1", "description": "Babel preset for all React plugins.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-preset-react", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-react"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}
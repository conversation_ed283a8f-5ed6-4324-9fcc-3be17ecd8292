# @babel/preset-react

> Babel preset for all React plugins.

See our website [@babel/preset-react](https://babeljs.io/docs/babel-preset-react) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20react%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/preset-react
```

or using yarn:

```sh
yarn add @babel/preset-react --dev
```

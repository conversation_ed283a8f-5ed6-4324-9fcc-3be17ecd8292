{"version": 3, "names": ["pirates", "require", "sourceMapSupport", "piratesRevert", "maps", "Object", "create", "installSourceMapSupport", "install", "handleUncaughtExceptions", "environment", "retrieveSourceMap", "filename", "map", "url", "<PERSON><PERSON><PERSON>", "compiling", "internalModuleCache", "_cache", "compileBabel7", "client", "code", "isLocalClient", "compile", "globalModuleCache", "inputCode", "result", "transform", "register", "opts", "_opts$extensions", "addHook", "bind", "exts", "extensions", "getDefaultExtensions", "ignoreNodeModules", "setOptions", "revert", "module", "exports"], "sources": ["../src/hook.cts"], "sourcesContent": ["import type { IClient, Options } from \"./types.cts\";\n\nimport pirates = require(\"pirates\");\nconst sourceMapSupport: typeof import(\"@cspotcode/source-map-support\") = process\n  .env.BABEL_8_BREAKING\n  ? require(\"@cspotcode/source-map-support\")\n  : require(\"source-map-support\");\n\nlet piratesRevert: () => void;\nconst maps = Object.create(null);\n\nfunction installSourceMapSupport() {\n  // @ts-expect-error assign to function\n  installSourceMapSupport = () => {};\n\n  sourceMapSupport.install({\n    handleUncaughtExceptions: false,\n    environment: \"node\",\n    retrieveSourceMap(filename: string) {\n      const map = maps?.[filename];\n      if (map) {\n        return { url: null, map: map };\n      } else {\n        return null;\n      }\n    },\n  });\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // Babel 7 compiles files in the same thread where it hooks `require()`,\n  // so we must prevent mixing Babel plugin dependencies with the files\n  // to be compiled.\n  // All the `!process.env.BABEL_8_BREAKING` code in this file is for\n  // this purpose.\n\n  const Module = require(\"node:module\");\n\n  let compiling = false;\n  const internalModuleCache = Module._cache;\n\n  // eslint-disable-next-line no-var\n  var compileBabel7 = function compileBabel7(\n    client: IClient,\n    code: string,\n    filename: string,\n  ) {\n    // @ts-expect-error Babel 7 property\n    if (!client.isLocalClient) return compile(client, code, filename);\n\n    if (compiling) return code;\n\n    const globalModuleCache = Module._cache;\n    try {\n      compiling = true;\n      Module._cache = internalModuleCache;\n      return compile(client, code, filename);\n    } finally {\n      compiling = false;\n      Module._cache = globalModuleCache;\n    }\n  };\n}\n\nfunction compile(client: IClient, inputCode: string, filename: string) {\n  const result = client.transform(inputCode, filename);\n\n  if (result === null) return inputCode;\n\n  const { code, map } = result;\n  if (map) {\n    maps[filename] = map;\n    installSourceMapSupport();\n  }\n  return code;\n}\n\nfunction register(client: IClient, opts: Options = {}) {\n  if (piratesRevert) piratesRevert();\n\n  piratesRevert = pirates.addHook(\n    (process.env.BABEL_8_BREAKING ? compile : compileBabel7).bind(null, client),\n    {\n      exts: opts.extensions ?? client.getDefaultExtensions(),\n      ignoreNodeModules: false,\n    },\n  );\n\n  client.setOptions(opts);\n}\n\nfunction revert() {\n  if (piratesRevert) piratesRevert();\n}\n\nexport = { register, revert };\n"], "mappings": ";;MAEOA,OAAO,GAAAC,OAAA,CAAW,SAAS;AAClC,MAAMC,gBAAgE,GAGlED,OAAO,CAAC,oBAAoB,CAAC;AAEjC,IAAIE,aAAyB;AAC7B,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAEhC,SAASC,uBAAuBA,CAAA,EAAG;EAEjCA,uBAAuB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAElCL,gBAAgB,CAACM,OAAO,CAAC;IACvBC,wBAAwB,EAAE,KAAK;IAC/BC,WAAW,EAAE,MAAM;IACnBC,iBAAiBA,CAACC,QAAgB,EAAE;MAClC,MAAMC,GAAG,GAAGT,IAAI,oBAAJA,IAAI,CAAGQ,QAAQ,CAAC;MAC5B,IAAIC,GAAG,EAAE;QACP,OAAO;UAAEC,GAAG,EAAE,IAAI;UAAED,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAEmC;EAOjC,MAAME,MAAM,GAAGd,OAAO,CAAC,QAAa,CAAC;EAErC,IAAIe,SAAS,GAAG,KAAK;EACrB,MAAMC,mBAAmB,GAAGF,MAAM,CAACG,MAAM;EAGzC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CACxCC,MAAe,EACfC,IAAY,EACZT,QAAgB,EAChB;IAEA,IAAI,CAACQ,MAAM,CAACE,aAAa,EAAE,OAAOC,OAAO,CAACH,MAAM,EAAEC,IAAI,EAAET,QAAQ,CAAC;IAEjE,IAAII,SAAS,EAAE,OAAOK,IAAI;IAE1B,MAAMG,iBAAiB,GAAGT,MAAM,CAACG,MAAM;IACvC,IAAI;MACFF,SAAS,GAAG,IAAI;MAChBD,MAAM,CAACG,MAAM,GAAGD,mBAAmB;MACnC,OAAOM,OAAO,CAACH,MAAM,EAAEC,IAAI,EAAET,QAAQ,CAAC;IACxC,CAAC,SAAS;MACRI,SAAS,GAAG,KAAK;MACjBD,MAAM,CAACG,MAAM,GAAGM,iBAAiB;IACnC;EACF,CAAC;AACH;AAEA,SAASD,OAAOA,CAACH,MAAe,EAAEK,SAAiB,EAAEb,QAAgB,EAAE;EACrE,MAAMc,MAAM,GAAGN,MAAM,CAACO,SAAS,CAACF,SAAS,EAAEb,QAAQ,CAAC;EAEpD,IAAIc,MAAM,KAAK,IAAI,EAAE,OAAOD,SAAS;EAErC,MAAM;IAAEJ,IAAI;IAAER;EAAI,CAAC,GAAGa,MAAM;EAC5B,IAAIb,GAAG,EAAE;IACPT,IAAI,CAACQ,QAAQ,CAAC,GAAGC,GAAG;IACpBN,uBAAuB,CAAC,CAAC;EAC3B;EACA,OAAOc,IAAI;AACb;AAEA,SAASO,QAAQA,CAACR,MAAe,EAAES,IAAa,GAAG,CAAC,CAAC,EAAE;EAAA,IAAAC,gBAAA;EACrD,IAAI3B,aAAa,EAAEA,aAAa,CAAC,CAAC;EAElCA,aAAa,GAAGH,OAAO,CAAC+B,OAAO,CACaZ,aAAa,CAAEa,IAAI,CAAC,IAAI,EAAEZ,MAAM,CAAC,EAC3E;IACEa,IAAI,GAAAH,gBAAA,GAAED,IAAI,CAACK,UAAU,YAAAJ,gBAAA,GAAIV,MAAM,CAACe,oBAAoB,CAAC,CAAC;IACtDC,iBAAiB,EAAE;EACrB,CACF,CAAC;EAEDhB,MAAM,CAACiB,UAAU,CAACR,IAAI,CAAC;AACzB;AAEA,SAASS,MAAMA,CAAA,EAAG;EAChB,IAAInC,aAAa,EAAEA,aAAa,CAAC,CAAC;AACpC;AAACoC,MAAA,CAAAC,OAAA,GAEQ;EAAEZ,QAAQ;EAAEU;AAAO,CAAC", "ignoreList": []}
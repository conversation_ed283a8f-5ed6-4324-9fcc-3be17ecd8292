{"version": 3, "names": ["babel", "require", "transform", "module", "exports", "handleMessage", "action", "payload", "DEFAULT_EXTENSIONS", "setOptions", "code", "filename", "transformSync", "Error"], "sources": ["../../src/worker/handle-message.cts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-enum-comparison */\nimport type { ACTIONS } from \"../types.cts\";\n\nconst babel = require(\"./babel-core.cjs\");\nimport transform = require(\"./transform.cjs\");\n\nexport = function handleMessage(action: ACTIONS, payload: any) {\n  switch (action) {\n    case \"GET_DEFAULT_EXTENSIONS\":\n      return babel.DEFAULT_EXTENSIONS;\n    case \"SET_OPTIONS\":\n      transform.setOptions(payload);\n      return;\n    case \"TRANSFORM\":\n      return transform.transform(payload.code, payload.filename);\n    case \"TRANSFORM_SYNC\":\n      if (!process.env.BABEL_8_BREAKING) {\n        // @ts-expect-error Babel 7\n        return transform.transformSync(payload.code, payload.filename);\n      }\n  }\n\n  throw new Error(`Unknown internal parser worker action: ${action}`);\n};\n"], "mappings": ";;AAGA,MAAMA,KAAK,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAAC,MACnCC,SAAS,GAAAD,OAAA,CAAW,iBAAiB;AAAAE,MAAA,CAAAC,OAAA,GAEnC,SAASC,aAAaA,CAACC,MAAe,EAAEC,OAAY,EAAE;EAC7D,QAAQD,MAAM;IACZ,KAAK,wBAAwB;MAC3B,OAAON,KAAK,CAACQ,kBAAkB;IACjC,KAAK,aAAa;MAChBN,SAAS,CAACO,UAAU,CAACF,OAAO,CAAC;MAC7B;IACF,KAAK,WAAW;MACd,OAAOL,SAAS,CAACA,SAAS,CAACK,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACI,QAAQ,CAAC;IAC5D,KAAK,gBAAgB;MACgB;QAEjC,OAAOT,SAAS,CAACU,aAAa,CAACL,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACI,QAAQ,CAAC;MAChE;EACJ;EAEA,MAAM,IAAIE,KAAK,CAAC,0CAA0CP,MAAM,EAAE,CAAC;AACrE,CAAC", "ignoreList": []}
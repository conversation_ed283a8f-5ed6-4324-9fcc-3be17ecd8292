{"version": 3, "names": ["cloneDeep", "require", "path", "fs", "babel", "registerCache", "nmRE", "escapeRegExp", "sep", "string", "replace", "cache", "transformOpts", "setOptions", "opts", "clear", "load", "get", "extensions", "Object", "assign", "caller", "name", "cwd", "resolve", "ignore", "undefined", "only", "cwdRE", "RegExp", "transform", "_x", "_x2", "_transform", "apply", "arguments", "_asyncToGenerator", "input", "filename", "loadOptionsAsync", "sourceRoot", "dirname", "cached", "store", "cacheLookup", "code", "map", "transformAsync", "sourceMaps", "ast", "module", "exports", "transformSync", "OptionManager", "init", "id", "value", "cache<PERSON>ey", "JSON", "stringify", "version", "env", "getEnv", "fileMtime", "statSync", "mtime", "set<PERSON>irty"], "sources": ["../../src/worker/transform.cts"], "sourcesContent": ["// @ts-expect-error no types\nimport cloneDeep = require(\"clone-deep\");\nimport path = require(\"node:path\");\nimport fs = require(\"node:fs\");\n\nconst babel = require(\"./babel-core.cjs\");\nconst registerCache = require(\"./cache.cjs\");\n\nconst nmRE = escapeRegExp(path.sep + \"node_modules\" + path.sep);\n\nfunction escapeRegExp(string: string) {\n  return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\");\n}\n\ntype CacheItem = { value: { code: string; map: any }; mtime: number };\n\nlet cache: Record<string, CacheItem>;\nlet transformOpts: any;\nfunction setOptions(opts: any) {\n  if (opts.cache === false && cache) {\n    registerCache.clear();\n    cache = null;\n  } else if (opts.cache !== false && !cache) {\n    registerCache.load();\n    cache = registerCache.get();\n  }\n\n  delete opts.cache;\n  delete opts.extensions;\n\n  transformOpts = {\n    ...opts,\n    caller: {\n      name: \"@babel/register\",\n      ...(opts.caller || {}),\n    },\n  };\n\n  let { cwd = \".\" } = transformOpts;\n\n  // Ensure that the working directory is resolved up front so that\n  // things don't break if it changes later.\n  cwd = transformOpts.cwd = path.resolve(cwd);\n\n  if (transformOpts.ignore === undefined && transformOpts.only === undefined) {\n    const cwdRE = escapeRegExp(cwd);\n\n    // Only compile things inside the current working directory.\n    transformOpts.only = [new RegExp(\"^\" + cwdRE, \"i\")];\n    // Ignore any node_modules inside the current working directory.\n    transformOpts.ignore = [\n      new RegExp(`^${cwdRE}(?:${path.sep}.*)?${nmRE}`, \"i\"),\n    ];\n  }\n}\n\nasync function transform(input: string, filename: string) {\n  const opts = await babel.loadOptionsAsync({\n    // sourceRoot can be overwritten\n    sourceRoot: path.dirname(filename) + path.sep,\n    ...cloneDeep(transformOpts),\n    filename,\n  });\n\n  // Bail out ASAP if the file has been ignored.\n  if (opts === null) return null;\n\n  const { cached, store } = cacheLookup(opts, filename);\n  if (cached) return cached;\n\n  const { code, map } = await babel.transformAsync(input, {\n    ...opts,\n    sourceMaps: opts.sourceMaps === undefined ? \"both\" : opts.sourceMaps,\n    ast: false,\n  });\n\n  return store({ code, map });\n}\n\nexport = { setOptions, transform };\n\nif (!process.env.BABEL_8_BREAKING) {\n  module.exports.transformSync = function (input: string, filename: string) {\n    const opts = new babel.OptionManager().init({\n      // sourceRoot can be overwritten\n      sourceRoot: path.dirname(filename) + path.sep,\n      ...cloneDeep(transformOpts),\n      filename,\n    });\n\n    // Bail out ASAP if the file has been ignored.\n    if (opts === null) return null;\n\n    const { cached, store } = cacheLookup(opts, filename);\n    if (cached) return cached;\n\n    const { code, map } = babel.transformSync(input, {\n      ...opts,\n      sourceMaps: opts.sourceMaps === undefined ? \"both\" : opts.sourceMaps,\n      ast: false,\n    });\n\n    return store({ code, map });\n  };\n}\n\nconst id = (value: unknown) => value;\n\nfunction cacheLookup(opts: unknown, filename: string) {\n  if (!cache) return { cached: null, store: id };\n\n  let cacheKey = `${JSON.stringify(opts)}:${babel.version}`;\n\n  const env = babel.getEnv();\n  if (env) cacheKey += `:${env}`;\n\n  const cached = cache[cacheKey];\n  const fileMtime = +fs.statSync(filename).mtime;\n\n  if (cached && cached.mtime === fileMtime) {\n    return { cached: cached.value, store: id };\n  }\n\n  return {\n    cached: null,\n    store(value: CacheItem[\"value\"]) {\n      cache[cacheKey] = { value, mtime: fileMtime };\n      registerCache.setDirty();\n      return value;\n    },\n  };\n}\n"], "mappings": ";;;;MACOA,SAAS,GAAAC,OAAA,CAAW,YAAY;AAAA,MAChCC,IAAI,GAAAD,OAAA,CAAW,MAAW;AAAA,MAC1BE,EAAE,GAAAF,OAAA,CAAW,IAAS;AAE7B,MAAMG,KAAK,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACzC,MAAMI,aAAa,GAAGJ,OAAO,CAAC,aAAa,CAAC;AAE5C,MAAMK,IAAI,GAAGC,YAAY,CAACL,IAAI,CAACM,GAAG,GAAG,cAAc,GAAGN,IAAI,CAACM,GAAG,CAAC;AAE/D,SAASD,YAAYA,CAACE,MAAc,EAAE;EACpC,OAAOA,MAAM,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACtD;AAIA,IAAIC,KAAgC;AACpC,IAAIC,aAAkB;AACtB,SAASC,UAAUA,CAACC,IAAS,EAAE;EAC7B,IAAIA,IAAI,CAACH,KAAK,KAAK,KAAK,IAAIA,KAAK,EAAE;IACjCN,aAAa,CAACU,KAAK,CAAC,CAAC;IACrBJ,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAIG,IAAI,CAACH,KAAK,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;IACzCN,aAAa,CAACW,IAAI,CAAC,CAAC;IACpBL,KAAK,GAAGN,aAAa,CAACY,GAAG,CAAC,CAAC;EAC7B;EAEA,OAAOH,IAAI,CAACH,KAAK;EACjB,OAAOG,IAAI,CAACI,UAAU;EAEtBN,aAAa,GAAAO,MAAA,CAAAC,MAAA,KACRN,IAAI;IACPO,MAAM,EAAAF,MAAA,CAAAC,MAAA;MACJE,IAAI,EAAE;IAAiB,GACnBR,IAAI,CAACO,MAAM,IAAI,CAAC,CAAC;EACtB,EACF;EAED,IAAI;IAAEE,GAAG,GAAG;EAAI,CAAC,GAAGX,aAAa;EAIjCW,GAAG,GAAGX,aAAa,CAACW,GAAG,GAAGrB,IAAI,CAACsB,OAAO,CAACD,GAAG,CAAC;EAE3C,IAAIX,aAAa,CAACa,MAAM,KAAKC,SAAS,IAAId,aAAa,CAACe,IAAI,KAAKD,SAAS,EAAE;IAC1E,MAAME,KAAK,GAAGrB,YAAY,CAACgB,GAAG,CAAC;IAG/BX,aAAa,CAACe,IAAI,GAAG,CAAC,IAAIE,MAAM,CAAC,GAAG,GAAGD,KAAK,EAAE,GAAG,CAAC,CAAC;IAEnDhB,aAAa,CAACa,MAAM,GAAG,CACrB,IAAII,MAAM,CAAC,IAAID,KAAK,MAAM1B,IAAI,CAACM,GAAG,OAAOF,IAAI,EAAE,EAAE,GAAG,CAAC,CACtD;EACH;AACF;AAAC,SAEcwB,SAASA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAAxB,WAAyBC,KAAa,EAAEC,QAAgB,EAAE;IACxD,MAAMxB,IAAI,SAASV,KAAK,CAACmC,gBAAgB,CAAApB,MAAA,CAAAC,MAAA;MAEvCoB,UAAU,EAAEtC,IAAI,CAACuC,OAAO,CAACH,QAAQ,CAAC,GAAGpC,IAAI,CAACM;IAAG,GAC1CR,SAAS,CAACY,aAAa,CAAC;MAC3B0B;IAAQ,EACT,CAAC;IAGF,IAAIxB,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;IAE9B,MAAM;MAAE4B,MAAM;MAAEC;IAAM,CAAC,GAAGC,WAAW,CAAC9B,IAAI,EAAEwB,QAAQ,CAAC;IACrD,IAAII,MAAM,EAAE,OAAOA,MAAM;IAEzB,MAAM;MAAEG,IAAI;MAAEC;IAAI,CAAC,SAAS1C,KAAK,CAAC2C,cAAc,CAACV,KAAK,EAAAlB,MAAA,CAAAC,MAAA,KACjDN,IAAI;MACPkC,UAAU,EAAElC,IAAI,CAACkC,UAAU,KAAKtB,SAAS,GAAG,MAAM,GAAGZ,IAAI,CAACkC,UAAU;MACpEC,GAAG,EAAE;IAAK,EACX,CAAC;IAEF,OAAON,KAAK,CAAC;MAAEE,IAAI;MAAEC;IAAI,CAAC,CAAC;EAC7B,CAAC;EAAA,OAAAb,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAAe,MAAA,CAAAC,OAAA,GAEQ;EAAEtC,UAAU;EAAEiB;AAAU,CAAC;AAEC;EACjCoB,MAAM,CAACC,OAAO,CAACC,aAAa,GAAG,UAAUf,KAAa,EAAEC,QAAgB,EAAE;IACxE,MAAMxB,IAAI,GAAG,IAAIV,KAAK,CAACiD,aAAa,CAAC,CAAC,CAACC,IAAI,CAAAnC,MAAA,CAAAC,MAAA;MAEzCoB,UAAU,EAAEtC,IAAI,CAACuC,OAAO,CAACH,QAAQ,CAAC,GAAGpC,IAAI,CAACM;IAAG,GAC1CR,SAAS,CAACY,aAAa,CAAC;MAC3B0B;IAAQ,EACT,CAAC;IAGF,IAAIxB,IAAI,KAAK,IAAI,EAAE,OAAO,IAAI;IAE9B,MAAM;MAAE4B,MAAM;MAAEC;IAAM,CAAC,GAAGC,WAAW,CAAC9B,IAAI,EAAEwB,QAAQ,CAAC;IACrD,IAAII,MAAM,EAAE,OAAOA,MAAM;IAEzB,MAAM;MAAEG,IAAI;MAAEC;IAAI,CAAC,GAAG1C,KAAK,CAACgD,aAAa,CAACf,KAAK,EAAAlB,MAAA,CAAAC,MAAA,KAC1CN,IAAI;MACPkC,UAAU,EAAElC,IAAI,CAACkC,UAAU,KAAKtB,SAAS,GAAG,MAAM,GAAGZ,IAAI,CAACkC,UAAU;MACpEC,GAAG,EAAE;IAAK,EACX,CAAC;IAEF,OAAON,KAAK,CAAC;MAAEE,IAAI;MAAEC;IAAI,CAAC,CAAC;EAC7B,CAAC;AACH;AAEA,MAAMS,EAAE,GAAIC,KAAc,IAAKA,KAAK;AAEpC,SAASZ,WAAWA,CAAC9B,IAAa,EAAEwB,QAAgB,EAAE;EACpD,IAAI,CAAC3B,KAAK,EAAE,OAAO;IAAE+B,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAEY;EAAG,CAAC;EAE9C,IAAIE,QAAQ,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC7C,IAAI,CAAC,IAAIV,KAAK,CAACwD,OAAO,EAAE;EAEzD,MAAMC,GAAG,GAAGzD,KAAK,CAAC0D,MAAM,CAAC,CAAC;EAC1B,IAAID,GAAG,EAAEJ,QAAQ,IAAI,IAAII,GAAG,EAAE;EAE9B,MAAMnB,MAAM,GAAG/B,KAAK,CAAC8C,QAAQ,CAAC;EAC9B,MAAMM,SAAS,GAAG,CAAC5D,EAAE,CAAC6D,QAAQ,CAAC1B,QAAQ,CAAC,CAAC2B,KAAK;EAE9C,IAAIvB,MAAM,IAAIA,MAAM,CAACuB,KAAK,KAAKF,SAAS,EAAE;IACxC,OAAO;MAAErB,MAAM,EAAEA,MAAM,CAACc,KAAK;MAAEb,KAAK,EAAEY;IAAG,CAAC;EAC5C;EAEA,OAAO;IACLb,MAAM,EAAE,IAAI;IACZC,KAAKA,CAACa,KAAyB,EAAE;MAC/B7C,KAAK,CAAC8C,QAAQ,CAAC,GAAG;QAAED,KAAK;QAAES,KAAK,EAAEF;MAAU,CAAC;MAC7C1D,aAAa,CAAC6D,QAAQ,CAAC,CAAC;MACxB,OAAOV,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}
import React from 'react';
import path from 'path';
const logoBase64 = require('../../logo-base64.json');

const Checkbox = ({ label, className, checked = false }) => (
    <div className={`flex items-center ${className}`} style={{ lineHeight: '1' }}>
        <div style={{ 
            width: '12px', 
            height: '12px', 
            border: '1px solid #000', 
            marginRight: '6px',
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxSizing: 'border-box',
            flexShrink: 0,
            minWidth: '12px',
            minHeight: '12px'
        }}>
            {checked && <span style={{ color: '#000', fontSize: '10px', fontWeight: 'bold' }}>X</span>}
        </div>
        <span style={{ whiteSpace: 'nowrap', lineHeight: '1' }}>{label}</span>
    </div>
);

const InputLine = ({ label, className, labelWidth, noLabel = false, value = '', style = {}, spacing = false }) => (
    <div className={`flex items-baseline ${className}`} style={{ alignItems: 'baseline', marginTop: spacing ? '-2px' : '-3px', ...style }}>
        {!noLabel && <span style={{ width: labelWidth || '70px', flexShrink: 0 }}>{label}</span>}
        <div style={{ 
            flexGrow: 1, 
            height: '1em',
            position: 'relative',
            minHeight: '16px'
        }}>
            <svg style={{
                position: 'absolute',
                bottom: '-4px',
                left: '0',
                right: '0',
                height: '1px',
                width: '100%'
            }}>
                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
            </svg>
            {value && <span style={{ position: 'absolute', bottom: '-5px', left: 0, fontSize: '11px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{value}</span>}
        </div>
    </div>
);

const SectionHeader = ({ children }) => (
    <div style={{ position: 'relative' }}>
        <h2 style={{ fontWeight: 'bold', fontSize: '13px', paddingBottom: '1px', marginTop: '2px', marginBottom: '0px' }}>
            {children}
        </h2>
        <div style={{ 
            position: 'absolute', 
            bottom: '25px', 
            left: 0, 
            right: 0, 
            borderBottom: '4px solid #bbb',
            height: '0px'
        }}></div>
    </div>
);

const SinaForm = ({ data = {} }) => {
    // Helper function to format dates as DD/MM/YYYY
    const formatDate = (dateString) => {
        if (!dateString) return '';
        
        // Check if it's a valid date format (YYYY-MM-DD)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateString)) {
            // If it's not a valid date format, return the original string
            return dateString;
        }
        
        const date = new Date(dateString);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            return dateString;
        }
        
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    // Destructure data with default values
    const {
        // Document info
        documentNumber = '',
        pageNumber = '1',
        totalPages = '2',
        
        // Owner (Eigentümer)
        ownerName1 = '',
        ownerName2 = '',
        ownerStreet = '',
        ownerCity = '',
        ownerPhone = '',
        
        // Administration (Verwaltung)
        adminName1 = '',
        adminName2 = '',
        adminStreet = '',
        adminCity = '',
        adminPhone = '',
        
        // Electrical Installer (Elektroinstallateur)
        installerAuthNumber = '',
        installerName1 = '',
        installerName2 = '',
        installerStreet = '',
        installerCity = '',
        installerPhone = '',
        
        // Control Body (Kontrollorgan)
        controlAuthNumber = '',
        controlName1 = '',
        controlName2 = '',
        controlStreet = '',
        controlCity = '',
        controlPhone = '',
        
        // Installation Location
        installationLocation = '',
        locationStreet = '',
        locationCity = '',
        buildingType = '',
        objectNumber = '',
        stockwerkLage = '',
        instAnzeigeNumber = '',
        instAnzeigeDate = '',
        buildingPart = false,
        buildingPartText = '',
        zev = false,
        zevText = '',
        
        // Controls
        kontrolleSchlusskontrolle = false,
        kontrolleAbnahmekontrolle = false,
        kontrollePeriodische = false,
        kontrolleExtra = false,
        kontrolleExtraText1 = '',
        kontrolleExtraText2 = '',
        datumSK = '',
        datumAKPK = '',
        
        // Control Periods
        period1Jahr = false,
        period3Jahre = false,
        period5Jahre = false,
        period5JahreSchIII = false,
        period10Jahre = false,
        period20Jahre = false,
        
        // Control Scope
        scopeNeuanlage = false,
        scopeErweiterung = false,
        scopeAenderung = false,
        scopeTemporaere = false,
        scopeSpezial = false,
        spezialinstText = '',
        
        // Technical data
        schutzTNS = false,
        schutzTNC = false,
        schutzTNCS = false,
        schutzSchIII = false,
        schIIIText = '',
        anlageteil = '',
        anschlussStrom = '',
        
        // Table data
        zaehlerNr = '',
        stromkundeNutzung = '',
        artCharakteristik = '',
        stromIN = '',
        stromKAnfang = '',
        stromKEnde = '',
        widerstandISO = '',
        
        // Signatures
        installerKontrollberechtigter = '',
        installerUnterschriftsberechtigter = '',
        installerDatum1 = '',
        installerDatum2 = '',
        controlKontrollberechtigter = '',
        controlUnterschriftsberechtigter = '',
        controlDatum1 = '',
        controlDatum2 = '',
        
        // Attachments
        beilageMessProtokoll = false,
        beilagePhotovoltaik = false,
        beilageSonstige = false,
        beilageSonstigeText = '',
        plombenEntfernt = false,
        verteilerEigentuemer = false,
        verteilerNetz = false,

        // Additional text fields
        anlageStromkreisText = '',
        
        // Final section
        eingangDatum = '',
        stichprobeJa = false,
        stichprobeNein = false,
        keineMaengel = false,
        maengelbericht = false,
        anlagePlombiert = false,
        datumVisum = ''
    } = data;

    // Use base64 encoded logos for reliable PDF rendering
    const logos = [
        { src: logoBase64.logo1, alt: 'Logo 1' },
        { src: logoBase64.logo2, alt: 'Logo 2' },
        { src: logoBase64.logo3, alt: 'Logo 3' },
        { src: logoBase64.logo4, alt: 'Logo 4' },
        { src: logoBase64.logo5, alt: 'Logo 5' },
        { src: logoBase64.logo6, alt: 'Logo 6' },
        { src: logoBase64.logo7, alt: 'Logo 7' }
    ];

    return (
        <div style={{ fontFamily: 'Arial, sans-serif', backgroundColor: '#fff', color: '#000', width: '100%', height: '100%', padding: '18px', fontSize: '10px', lineHeight: '1.2', boxSizing: 'border-box', position: 'relative' }}>
            
            {/* Logos */}
            <div style={{ position: 'absolute', left: '5px', top: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                {logos.map((logo, index) => (
                    <img key={index} src={logo.src} alt={logo.alt} style={{ width: '45px', height: 'auto' }} />
                ))}
            </div>
            
            {/* Header */}
            <div style={{ marginLeft: '35px' }}>
                <div>
                    <h1 style={{ fontSize: '20px', fontWeight: 'bold', margin: 0 }}>Sicherheitsnachweis Elektroinstallationen (SiNa)</h1>
                    <p style={{ margin: '0px 0 0 0', fontSize: '11px' }}>gemäss Verordnung über elektrische Niederspannungsinstallationen (NIV, SR 734.27)</p>
                </div>
            </div>

            <div className="flex justify-between items-end" style={{ borderBottom: '4px solid #bbb', paddingBottom: '4px', marginLeft: '35px', marginTop: '2px' }}>
                <div>
                    <span>Pro Zählerstromkreis ein SiNa</span>
                    <span style={{ marginLeft: '40px' }}>Nr.</span>
                    <div style={{ 
                        width: '187px', 
                        marginLeft: '5px',
                        position: 'relative',
                        minHeight: '16px',
                        display: 'inline-block',
                        top: '2px'
                    }}>
                        <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#000" strokeWidth="0.1" />
                        </svg>
                        {documentNumber && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{documentNumber}</span>}
                    </div>
                </div>
                <div className="flex items-center">
                    <span style={{ marginRight: '5px' }}>Seite</span>
                    <div style={{ position: 'relative', display: 'inline-block', width: '30px' }}>
                        <span style={{ display: 'inline-block', textAlign: 'center', color: '#0000FF', fontWeight: 'bold', width: '100%' }}>{pageNumber}</span>
                        <svg style={{ position: 'absolute', bottom: '-2px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#000" strokeWidth="0.1" />
                        </svg>
                    </div>
                    <span style={{ margin: '0 5px' }}>von</span>
                    <div style={{ position: 'relative', display: 'inline-block', width: '30px' }}>
                        <span style={{ display: 'inline-block', textAlign: 'center', color: '#0000FF', fontWeight: 'bold', width: '100%' }}>{totalPages}</span>
                        <svg style={{ position: 'absolute', bottom: '-2px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#000" strokeWidth="0.1" />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Eigentümer / Verwaltung */}
            <div className="grid grid-cols-2 gap-x-7" style={{ marginLeft: '35px', marginTop: '-2px' }}>
                <div>
                    <div className="flex justify-between items-baseline">
                        <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-3px', marginBottom: '1px' }}>Eigentümer der Installation</h3>
                        <div className="flex items-baseline">
                            <span>Tel.Nr.</span>
                            <div style={{ 
                                width: '112px', 
                                marginLeft: '5px',
                                position: 'relative',
                                minHeight: '16px',
                                top: '2px'
                            }}>
                                <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {ownerPhone && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{ownerPhone}</span>}
                            </div>
                        </div>
                    </div>
                    <InputLine label="Name 1" value={ownerName1} />
                    <InputLine label="Name 2" value={ownerName2} />
                    <InputLine label="Strasse, Nr." value={ownerStreet} />
                    <InputLine label="PLZ, Ort" value={ownerCity} spacing={true} />
                </div>
                <div>
                    <div className="flex justify-between items-baseline">
                        <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-3px', marginBottom: '1px' }}>Verwaltung</h3>
                        <div className="flex items-baseline">
                            <span>Tel. Nr.</span>
                            <div style={{ 
                                width: '112px', 
                                marginLeft: '5px',
                                position: 'relative',
                                minHeight: '16px',
                                top: '2px'
                            }}>
                                <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {adminPhone && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{adminPhone}</span>}
                            </div>
                        </div>
                    </div>
                    <InputLine label="Name 1" value={adminName1} />
                    <InputLine label="Name 2" value={adminName2} />
                    <InputLine label="Strasse, Nr." value={adminStreet} />
                    <InputLine label="PLZ, Ort" value={adminCity} spacing={true} />
                </div>
            </div>

            {/* Elektroinstallateur / Kontrollorgan */}
            <div className="grid grid-cols-2 gap-x-7" style={{ borderTop: '4px solid #bbb', paddingTop: '0px', marginLeft: '35px', marginTop: '7px' }}>
                <div>
                        <div className="flex justify-between items-baseline">
                            <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-5px', marginBottom: '1px' }}>Elektroinstallateur</h3>
                            <div className="flex items-baseline">
                                <span>Bew.- Nr. I -</span>
                                <div style={{ 
                                    width: '93px', 
                                    marginLeft: '5px',
                                    position: 'relative',
                                    minHeight: '16px',
                                    top: '2px'
                                }}>
                                    <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                        <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                    </svg>
                                    {installerAuthNumber && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{installerAuthNumber}</span>}
                                </div>
                            </div>
                        </div>
                        <InputLine label="Name 1" value={installerName1} />
                        <InputLine label="Name 2" value={installerName2} />
                        <InputLine label="Strasse, Nr." value={installerStreet} />
                        <InputLine label="PLZ, Ort" value={installerCity} spacing={true} />
                        <InputLine label="Tel Nr." value={installerPhone} />
                </div>
                <div>
                        <div className="flex justify-between items-baseline">
                            <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-5px', marginBottom: '1px' }}>Unabhängiges Kontrollorgan</h3>
                            <div className="flex items-baseline">
                                <span>Bew.- Nr. K -</span>
                                <div style={{ 
                                    width: '75px', 
                                    marginLeft: '5px',
                                    position: 'relative',
                                    minHeight: '16px',
                                    top: '2px'
                                }}>
                                    <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                        <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                    </svg>
                                    {controlAuthNumber && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{controlAuthNumber}</span>}
                                </div>
                            </div>
                        </div>
                        <InputLine label="Name 1" value={controlName1} />
                        <InputLine label="Name 2" value={controlName2} />
                        <InputLine label="Strasse, Nr." value={controlStreet} />
                        <InputLine label="PLZ, Ort" value={controlCity} spacing={true} />
                        <InputLine label="Tel. Nr." value={controlPhone} />
                </div>
            </div>

            {/* Ort der Installation */}
            <div className="grid grid-cols-2 gap-x-7" style={{ borderTop: '4px solid #bbb', paddingTop: '0px', marginLeft: '37px', marginTop: '5px', position: 'relative' }}>
                <div>
                    <div className="flex justify-between items-baseline">
                        <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-2px', marginBottom: '1px', width: '112px' }}>Ort der Installation</h3>
                        <div style={{ 
                            flexGrow: 1, 
                            position: 'relative',
                            minHeight: '16px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {installationLocation && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{installationLocation}</span>}
                        </div>
                    </div>
                    <InputLine label="Strasse, Nr." value={locationStreet} />
                    <InputLine label="PLZ, Ort" value={locationCity} spacing={true} />
                </div>
                <div>
                    <div className="flex justify-between items-baseline" style={{ marginTop: '-4px' }}>
                        <span style={{ width: '55px', marginTop: '1px' }}>Gebäudeart</span>
                        <div style={{ 
                            flexGrow: 1, 
                            position: 'relative',
                            minHeight: '16px',
                            marginLeft: '5px',
                            marginRight: '5px',
                            top: '3px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {buildingType && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{buildingType}</span>}
                        </div>
                    </div>
                    <div className="flex items-baseline">
                        <span style={{ width: '55px' }}>Objekt Nr.</span>
                        <div style={{ 
                            flexGrow: 1, 
                            position: 'relative',
                            minHeight: '16px',
                            display: 'flex',
                            alignItems: 'baseline',
                            marginLeft: '5px',
                            marginRight: '5px',
                            top: '2px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            <span style={{ position: 'absolute', bottom: '-1px', left: '0px', fontSize: '10px', maxWidth: '100px', overflow: 'hidden', whiteSpace: 'nowrap' }}>{objectNumber}</span>
                            <span style={{ position: 'absolute', bottom: '-1px', right: '100px', fontSize: '10px', fontWeight: 'normal' }}>Stockwerk / Lage</span>
                            <span style={{ position: 'absolute', bottom: '-1px', right: '0px', fontSize: '10px', maxWidth: '100px', overflow: 'hidden', whiteSpace: 'nowrap' }}>{stockwerkLage}</span>
                        </div>
                    </div>
                    <div className="flex justify-between items-baseline">
                        <span style={{ width: '130px' }}>Inst.-Anzeige Nr. / vom:</span>
                        <div style={{ 
                            flexGrow: 1, 
                            position: 'relative',
                            minHeight: '16px',
                            top: '2px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {instAnzeigeNumber && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{instAnzeigeNumber} {formatDate(instAnzeigeDate)}</span>}
                        </div>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                        <Checkbox label="Gebäudeteil" checked={buildingPart} />
                        <div style={{ flexGrow: 1, marginLeft: '5px', marginTop: '3px', position: 'relative', top: '-1px', minHeight: '16px' }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {buildingPartText && <span style={{ position: 'absolute', bottom: '2px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{buildingPartText}</span>}
                        </div>
                        <Checkbox label="ZEV" className="ml-4" checked={zev} />
                        <div style={{ flexGrow: 1, marginLeft: '5px', marginTop: '3px', position: 'relative', top: '-1px', minHeight: '16px' }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {zevText && <span style={{ position: 'absolute', bottom: '2px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{zevText}</span>}
                        </div>
                    </div>
                </div>
            </div>

            {/* Kontrollen */}
            <div className="grid grid-cols-3 gap-x-5" style={{ borderTop: '4px solid #bbb', paddingTop: '1px', marginLeft: '35px', marginTop: '2px' }}>
                <div>
                    <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '2px', marginBottom: '1px' }}>Durchgeführte Kontrollen</h3>
                    <Checkbox label="Schlusskontrolle SK" className="mt-1" checked={kontrolleSchlusskontrolle} />
                    <Checkbox label="Abnahmekontrolle AK" className="mt-1" checked={kontrolleAbnahmekontrolle} />
                    <Checkbox label="Periodische Kontrolle PK" className="mt-1" checked={kontrollePeriodische} />
                    <div className="flex items-center mt-1">
                        <div style={{ 
                            width: '12px', 
                            height: '12px', 
                            border: '1px solid #000', 
                            marginRight: '6px',
                            backgroundColor: 'transparent',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxSizing: 'border-box',
                            flexShrink: 0,
                            minWidth: '12px',
                            minHeight: '12px'
                        }}>
                            {kontrolleExtra && <span style={{ color: '#000', fontSize: '10px', fontWeight: 'bold' }}>X</span>}
                        </div>
                        <div style={{ 
                            width: '120px', 
                            position: 'relative',
                            minHeight: '16px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {kontrolleExtraText1 && (() => {
                                const words = kontrolleExtraText1.split(' ');
                                let line = '';
                                let wordIndex = 0;
                                
                                // First line: max 15 characters, complete words only
                                while (wordIndex < words.length) {
                                    const nextWord = words[wordIndex];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 15) {
                                        line = testLine;
                                        wordIndex++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            })()}
                        </div>
                    </div>
                    <div style={{ marginTop: '20px', width: '120px', marginLeft: '18px', position: 'relative', minHeight: '16px' }}>
                        <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                        {kontrolleExtraText1 && (() => {
                            const words = kontrolleExtraText1.split(' ');
                            let firstLineWordCount = 0;
                            let firstLine = '';
                            
                            // Calculate first line word count
                            while (firstLineWordCount < words.length) {
                                const nextWord = words[firstLineWordCount];
                                const testLine = firstLine + (firstLine ? ' ' : '') + nextWord;
                                if (testLine.length <= 15) {
                                    firstLine = testLine;
                                    firstLineWordCount++;
                                } else {
                                    break;
                                }
                            }
                            
                            // Second line: max 60 characters, complete words only
                            if (firstLineWordCount < words.length) {
                                let line = '';
                                let wordIndex = firstLineWordCount;
                                
                                while (wordIndex < words.length) {
                                    const nextWord = words[wordIndex];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 60) {
                                        line = testLine;
                                        wordIndex++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', width: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            }
                        })()}
                    </div>
                    
                    {/* Datum SK moved here */}
                    <div className="flex items-baseline" style={{ marginTop: '15px' }}>
                        <span style={{ fontWeight: 'bold', position: 'relative', top: '10px' }}>Datum SK:</span>
                        <div style={{ 
                            flexGrow: 1, 
                            marginLeft: '5px',
                            position: 'relative',
                            minHeight: '16px',
                            top: '15px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {datumSK && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{formatDate(datumSK)}</span>}
                        </div>
                    </div>
                </div>
                <div style={{ marginLeft: '-45px' }}>
                    <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '2px', marginBottom: '1px' }}>Kontrollperiode</h3>
                    <Checkbox label="1 Jahr" className="mt-1" checked={period1Jahr} />
                    <Checkbox label="3 Jahre" className="mt-2" checked={period3Jahre} />
                    <Checkbox label="5 Jahre" className="mt-2" checked={period5Jahre} />
                    <Checkbox label="5 Jahre (Sch III)" className="mt-2" checked={period5JahreSchIII} />
                    <Checkbox label="10 Jahre" className="mt-2" checked={period10Jahre} />
                    <Checkbox label="20 Jahre" className="mt-2" checked={period20Jahre} />
                </div>
                <div style={{ marginLeft: '-110px' }}>
                    <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '2px', marginBottom: '2px', whiteSpace: 'nowrap' }}>Kontrollumfang / Ausgeführte Installation</h3>
                    <div className="grid grid-cols-3" style={{ marginTop: '3px', gap: '15px' }}>
                        <div>
                            <Checkbox label="Neuanlage" checked={scopeNeuanlage} />
                            <Checkbox label="Temporäre Anlage" className="mt-1" checked={scopeTemporaere} />
                        </div>
                        <div>
                            <Checkbox label="Erweiterung" checked={scopeErweiterung} />
                            <div className="flex items-center mt-1">
                                <div style={{ marginLeft: '60px' }}>
                                    <Checkbox label="Spezialinst." checked={scopeSpezial} />
                                </div>
                                <div style={{ flexGrow: 1, marginLeft: '10px', marginRight: '-110px', marginTop: '13px', position: 'relative', minHeight: '16px' }}>
                                    <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                        <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                    </svg>
                                    {spezialinstText && (() => {
                                        const words = spezialinstText.split(' ');
                                        let line = '';
                                        let wordIndex = 0;
                                        
                                        // First line: max 15 characters, complete words only
                                        while (wordIndex < words.length) {
                                            const nextWord = words[wordIndex];
                                            const testLine = line + (line ? ' ' : '') + nextWord;
                                            if (testLine.length <= 15) {
                                                line = testLine;
                                                wordIndex++;
                                            } else {
                                                break;
                                            }
                                        }
                                        
                                        return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                                    })()}
                                </div>
                            </div>
                        </div>
                        <div style={{ width: '100%', maxWidth: '150px', overflow: 'hidden' }}>
                            <Checkbox label="Änderung / Umbau" checked={scopeAenderung} />
                        </div>
                    </div>
                    <div style={{ marginTop: '5px', position: 'relative', minHeight: '16px' }}>
                        {spezialinstText && (() => {
                            const words = spezialinstText.split(' ');
                            let firstLineWordCount = 0;
                            let firstLine = '';
                            
                            // Calculate first line word count
                            while (firstLineWordCount < words.length) {
                                const nextWord = words[firstLineWordCount];
                                const testLine = firstLine + (firstLine ? ' ' : '') + nextWord;
                                if (testLine.length <= 15) {
                                    firstLine = testLine;
                                    firstLineWordCount++;
                                } else {
                                    break;
                                }
                            }
                            
                            // Second line: max 60 characters, complete words only
                            if (firstLineWordCount < words.length) {
                                let line = '';
                                let wordIndex = firstLineWordCount;
                                
                                while (wordIndex < words.length) {
                                    const nextWord = words[wordIndex];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 60) {
                                        line = testLine;
                                        wordIndex++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', width: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            }
                        })()}
                        <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                    </div>
                    <div style={{ marginTop: '5px', position: 'relative', minHeight: '16px' }}>
                        {spezialinstText && (() => {
                            const words = spezialinstText.split(' ');
                            let processedWords = 0;
                            
                            // Calculate first line
                            let firstLine = '';
                            while (processedWords < words.length) {
                                const nextWord = words[processedWords];
                                const testLine = firstLine + (firstLine ? ' ' : '') + nextWord;
                                if (testLine.length <= 15) {
                                    firstLine = testLine;
                                    processedWords++;
                                } else {
                                    break;
                                }
                            }
                            
                            // Calculate second line
                            let secondLine = '';
                            while (processedWords < words.length) {
                                const nextWord = words[processedWords];
                                const testLine = secondLine + (secondLine ? ' ' : '') + nextWord;
                                if (testLine.length <= 60) {
                                    secondLine = testLine;
                                    processedWords++;
                                } else {
                                    break;
                                }
                            }
                            
                            // Third line: max 60 characters
                            if (processedWords < words.length) {
                                let line = '';
                                
                                while (processedWords < words.length) {
                                    const nextWord = words[processedWords];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 60) {
                                        line = testLine;
                                        processedWords++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', width: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            }
                        })()}
                        <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                    </div>
                    <div style={{ marginTop: '5px', position: 'relative', minHeight: '16px' }}>
                        {spezialinstText && (() => {
                            const words = spezialinstText.split(' ');
                            let processedWords = 0;
                            
                            // Calculate lines 1-3
                            for (let lineNum = 0; lineNum < 3; lineNum++) {
                                let line = '';
                                const maxChars = lineNum === 0 ? 15 : 60;
                                
                                while (processedWords < words.length) {
                                    const nextWord = words[processedWords];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= maxChars) {
                                        line = testLine;
                                        processedWords++;
                                    } else {
                                        break;
                                    }
                                }
                            }
                            
                            // Fourth line: max 60 characters
                            if (processedWords < words.length) {
                                let line = '';
                                
                                while (processedWords < words.length) {
                                    const nextWord = words[processedWords];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 60) {
                                        line = testLine;
                                        processedWords++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', width: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            }
                        })()}
                        <svg style={{ position: 'absolute', bottom: '-4px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                    </div>
                    <div style={{ marginTop: '5px', position: 'relative', minHeight: '16px' }}>
                        {spezialinstText && (() => {
                            const words = spezialinstText.split(' ');
                            let processedWords = 0;
                            
                            // Calculate lines 1-4
                            for (let lineNum = 0; lineNum < 4; lineNum++) {
                                let line = '';
                                const maxChars = lineNum === 0 ? 15 : 60;
                                
                                while (processedWords < words.length) {
                                    const nextWord = words[processedWords];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= maxChars) {
                                        line = testLine;
                                        processedWords++;
                                    } else {
                                        break;
                                    }
                                }
                            }
                            
                            // Fifth line: max 60 characters
                            if (processedWords < words.length) {
                                let line = '';
                                
                                while (processedWords < words.length) {
                                    const nextWord = words[processedWords];
                                    const testLine = line + (line ? ' ' : '') + nextWord;
                                    if (testLine.length <= 60) {
                                        line = testLine;
                                        processedWords++;
                                    } else {
                                        break;
                                    }
                                }
                                
                                return <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', width: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{line}</span>;
                            }
                        })()}
                        <svg style={{ position: 'absolute', bottom: '-1px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                    </div>
                    
                    {/* Datum AK / PK moved here */}
                    <div className="flex items-baseline" style={{ marginTop: '15px' }}>
                        <span style={{ fontWeight: 'bold', position: 'relative', top: '-18px' }}>Datum AK / PK:</span>
                        <div style={{ 
                            flexGrow: 1, 
                            marginLeft: '5px',
                            position: 'relative',
                            minHeight: '16px',
                            top: '-13px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {datumAKPK && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{formatDate(datumAKPK)}</span>}
                        </div>
                    </div>
                </div>
            </div>

            {/* Technische Angaben */}
            <div style={{ marginLeft: '35px', marginTop: '-5px', position: 'relative' }}>
                <div style={{ 
                    borderBottom: '4px solid #bbb',
                    marginBottom: '2px',
                    marginTop: '-10px'
                }}></div>
                <div className="flex items-center">
                    <h2 style={{ fontWeight: 'bold', fontSize: '13px', marginTop: '2px', marginBottom: '0px', marginRight: '20px' }}>Technische Angaben</h2>
                    <span>Schutz-System:</span>
                    <Checkbox label="TN-S" className="ml-8" checked={schutzTNS} />
                    <Checkbox label="TN-C" className="ml-8" checked={schutzTNC} />
                    <Checkbox label="TN-C-S" className="ml-8" checked={schutzTNCS} />
                    <Checkbox label="Sch III" className="ml-8" checked={schutzSchIII} />
                    <div style={{ 
                        flexGrow: 1, 
                        marginLeft: '10px',
                        position: 'relative',
                        minHeight: '16px'
                    }}>
                        <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                        {schIIIText && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{schIIIText}</span>}
                    </div>
                </div>
                <div className="flex items-baseline" style={{ marginTop: '2px', marginLeft: '440px' }}>
                    <span style={{ position: 'relative', top: '-4px' }}>Anlageteil:</span>
                    <div style={{ 
                        width: '240px', 
                        marginLeft: '5px',
                        position: 'relative',
                        minHeight: '16px'
                    }}>
                        <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                            <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                        </svg>
                        {anlageteil && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{anlageteil}</span>}
                    </div>
                </div>
            </div>
            <div className="flex items-baseline mt-1" style={{ marginLeft: '35px', position: 'relative', top: '-28px' }}>
                <span>Anschlussüberstromunterbrecher I<sub>N</sub></span>
                <div style={{ 
                    width: '93px', 
                    marginLeft: '5px',
                    position: 'relative',
                    minHeight: '16px',
                    top: '3px'
                }}>
                    <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                        <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                    </svg>
                    {anschlussStrom && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{anschlussStrom}</span>}
                </div>
                <span className="ml-2">A</span>
            </div>

            {/* Table */}
            <table style={{ 
                width: 'calc(100% - 33.66px)', 
                borderCollapse: 'collapse', 
                border: '2px solid #000', 
                marginTop: '-25px', 
                marginLeft: '35px',
                marginRight: '0px',
                fontSize: '9px',
                backgroundColor: '#fff'
            }}>
                <thead>
                    <tr>
                        <th colSpan="2" style={{
                            border: '2px solid #000',
                            padding: '4px',
                            textAlign: 'left',
                            fontWeight: 'bold',
                            verticalAlign: 'top',
                            width: '45%'
                        }}>
                            Anlage / Stromkreis:
                            <div style={{ marginTop: '3px', position: 'relative', minHeight: '16px' }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {anlageStromkreisText && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap', fontWeight: 'normal' }}>{anlageStromkreisText}</span>}
                            </div>
                        </th>
                        <th colSpan="2" style={{ 
                            border: '2px solid #000', 
                            padding: '4px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            verticalAlign: 'top',
                            width: '25%'
                        }}>
                            Überstrom-Schutzeinrichtung am<br />Anschlusspunkt der Installation
                        </th>
                        <th rowSpan="2" style={{ 
                            border: '2px solid #000', 
                            padding: '4px', 
                            textAlign: 'left', 
                            fontWeight: 'normal',
                            verticalAlign: 'top',
                            fontSize: '8px',
                            paddingLeft: '8px',
                            width: '10%'
                        }}>
                            <div style={{ textAlign: 'center' }}>
                                I<span style={{ marginLeft: '3px', fontSize: '8px', position: 'relative', top: '2px' }}>K</span><span style={{ marginLeft: '0px', fontSize: '8px', position: 'relative', top: '2px' }}> Anfang</span>
                            </div>
                            <div style={{ position: 'relative', marginTop: '8px', textAlign: 'center' }}>
                                <span style={{ fontSize: '7px' }}>L-PE</span> 
                                <span style={{ fontSize: '11px', position: 'relative', top: '-2px', left: '2px' }}>[A]</span>
                            </div>
                        </th>
                        <th rowSpan="2" style={{ 
                            border: '2px solid #000', 
                            padding: '4px', 
                            fontWeight: 'normal',
                            verticalAlign: 'top',
                            fontSize: '8px',
                            position: 'relative',
                            width: '12%'
                        }}>
                            <div style={{ textAlign: 'center' }}>
                                I<span style={{ marginLeft: '3px', fontSize: '8px', position: 'relative', top: '2px' }}>K</span><span style={{ marginLeft: '0px', fontSize: '8px', position: 'relative', top: '2px' }}> Ende</span>
                            </div>
                            <div style={{ position: 'relative', marginTop: '8px', textAlign: 'center' }}>
                                <span style={{ fontSize: '7px' }}>L-</span>
                                <span style={{ fontSize: '7px' }}>PE</span> 
                                <span style={{ fontSize: '11px', position: 'relative', top: '-2px', left: '2px' }}>[A]</span>
                            </div>
                        </th>
                        <th rowSpan="2" style={{ 
                            border: '2px solid #000', 
                            padding: '4px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            verticalAlign: 'middle',
                            fontSize: '8px',
                            width: '8%'
                        }}>
                            <div style={{ textAlign: 'center' }}>
                                R<sub>ISO</sub><br />[M Ohm]
                            </div>
                        </th>
                    </tr>
                    <tr>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            fontSize: '8px',
                            width: '22.5%'
                        }}>
                            Zähler Nr.
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            fontSize: '8px',
                            width: '22.5%'
                        }}>
                            Stromkunde / Nutzung:
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            fontSize: '8px',
                            width: '15%'
                        }}>
                            Art, Charakteristik
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            textAlign: 'center', 
                            fontWeight: 'normal',
                            fontSize: '8px',
                            width: '10%'
                        }}>
                            I<sub>N</sub> [A]
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr style={{ height: '40px' }}>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '150px'
                        }}>
                            {zaehlerNr}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '150px'
                        }}>
                            {stromkundeNutzung}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '120px'
                        }}>
                            {artCharakteristik}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            textAlign: 'center',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '60px'
                        }}>
                            {stromIN}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            textAlign: 'center',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '60px'
                        }}>
                            {stromKAnfang}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            textAlign: 'center',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '60px'
                        }}>
                            {stromKEnde}
                        </td>
                        <td style={{ 
                            border: '2px solid #000', 
                            padding: '2px', 
                            fontSize: '9px',
                            textAlign: 'center',
                            verticalAlign: 'top',
                            wordWrap: 'break-word',
                            overflow: 'hidden',
                            maxWidth: '60px'
                        }}>
                            {widerstandISO}
                        </td>
                    </tr>
                </tbody>
            </table>

            {/* Gray line after table */}
            <div style={{ 
                borderBottom: '4px solid #bbb', 
                marginTop: '6px', 
                marginLeft: '35px'
            }}></div>

            {/* Disclaimer */}
            <div className="text-xs" style={{ marginLeft: '35px', marginTop: '1px' }}>
                <p style={{ fontWeight: 'bold' }}>Die Unterzeichner bestätigen, dass die Installationen gemäss NIV (insb. Art. 3 und 4) und den gültigen Normen geprüft wurden und den anerkannten Regeln der Technik entsprechen.</p>
                <p className="mt-2">Dieses Dokument bildet den Sicherheitsnachweis für die erwähnten elektrischen Installationen im Sinne der NIV und ist vom Eigentümer bis zur nächsten (periodischen) Kontrolle aufzubewahren. Wer vorgeschrieben Kontrollen nicht oder in schwerwiegender Weise nicht korrekt ausführt oder Installationen mit gefährlichen Mängel dem Eigentümer übergibt, macht sich strafbar (NIV Art. 42 c).</p>
            </div>

            {/* Signatures */}
            <div className="grid grid-cols-2 gap-x-7" style={{ borderTop: '4px solid #bbb', paddingTop: '2px', marginLeft: '35px', marginTop: '-1px' }}>
                <div>
                    <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-2px' }}>Unterschriften Elektroinstallateur</h3>
                    <div className="flex" style={{ marginTop: '-7px' }}>
                        <span style={{ fontSize: '10px', width: '48%' }}>Kontrollberechtigter</span>
                        <span style={{ fontSize: '10px', width: '48%', marginLeft: '4%' }}>Unterschriftsberechtigter</span>
                    </div>
                    <div className="flex justify-between" style={{ marginTop: '30px' }}>
                        <div style={{ width: '48%' }}>
                            <div style={{ height: '40px', marginBottom: '5px', position: 'relative' }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {installerKontrollberechtigter && <span style={{ position: 'absolute', bottom: '5px', left: 0, fontSize: '10px' }}>{installerKontrollberechtigter}</span>}
                            </div>
                            <div style={{ fontSize: '10px' }}>Name Vorname (Blockschrift)</div>
                            <div style={{ fontSize: '10px', marginTop: '2px' }}>Datum: {formatDate(installerDatum1)}</div>
                        </div>
                        <div style={{ width: '48%' }}>
                            <div style={{ height: '40px', marginBottom: '5px', position: 'relative' }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {installerUnterschriftsberechtigter && <span style={{ position: 'absolute', bottom: '5px', left: 0, fontSize: '10px' }}>{installerUnterschriftsberechtigter}</span>}
                            </div>
                            <div style={{ fontSize: '10px' }}>Name Vorname (Blockschrift)</div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 style={{ fontWeight: 'bold', fontSize: '12px', marginTop: '-2px' }}>Unterschriften unabhängiges Kontrollorgan</h3>
                    <div className="flex" style={{ marginTop: '-7px' }}>
                        <span style={{ fontSize: '10px', width: '48%' }}>Kontrollberechtigter</span>
                        <span style={{ fontSize: '10px', width: '48%', marginLeft: '4%' }}>Unterschriftsberechtigter</span>
                    </div>
                    <div className="flex justify-between" style={{ marginTop: '30px' }}>
                        <div style={{ width: '48%' }}>
                            <div style={{ height: '40px', marginBottom: '5px', position: 'relative' }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {controlKontrollberechtigter && <span style={{ position: 'absolute', bottom: '5px', left: 0, fontSize: '10px' }}>{controlKontrollberechtigter}</span>}
                            </div>
                            <div style={{ fontSize: '10px' }}>Name Vorname (Blockschrift)</div>
                            <div style={{ fontSize: '10px', marginTop: '2px' }}>Datum: {formatDate(controlDatum1)}</div>
                        </div>
                        <div style={{ width: '48%' }}>
                            <div style={{ height: '40px', marginBottom: '5px', position: 'relative' }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {controlUnterschriftsberechtigter && <span style={{ position: 'absolute', bottom: '5px', left: 0, fontSize: '10px' }}>{controlUnterschriftsberechtigter}</span>}
                            </div>
                            <div style={{ fontSize: '10px' }}>Name Vorname (Blockschrift)</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Beilagen */}
            <div className="grid grid-cols-2 gap-x-7" style={{ borderTop: '4px solid #000', paddingTop: '2px', marginLeft: '35px', marginTop: '7px' }}>
                <div>
                    <div className="flex">
                        <span className="font-bold" style={{ width: '60px' }}>Beilagen:</span>
                        <div className="flex-grow">
                            <Checkbox label="Mess- + Prüfprotokoll" checked={beilageMessProtokoll} />
                            <Checkbox label="Mess- + Prüfprotokoll Photovoltaik" className="mt-1" checked={beilagePhotovoltaik} />
                            <div className="flex items-center mt-1">
                                <div style={{
                                    width: '12px',
                                    height: '12px',
                                    border: '1px solid #000',
                                    marginRight: '6px',
                                    backgroundColor: 'transparent',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    boxSizing: 'border-box',
                                    flexShrink: 0,
                                    minWidth: '12px',
                                    minHeight: '12px'
                                }}>
                                    {beilageSonstige && <span style={{ color: '#000', fontSize: '10px', fontWeight: 'bold' }}>X</span>}
                                </div>
                                <div style={{ flexGrow: 1, marginLeft: '2px', marginTop: '3px', position: 'relative', minHeight: '16px' }}>
                                    <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                        <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                    </svg>
                                    {beilageSonstigeText && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{beilageSonstigeText}</span>}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <Checkbox label="Plomben wurden entfernt" checked={plombenEntfernt} />
                    <div className="flex mt-1">
                        <span style={{ width: '50px' }}>Verteiler:</span>
                        <div className="flex-grow">
                            <Checkbox label="SiNa + Zusatzdokument an Eigentümer / Verwaltung" checked={verteilerEigentuemer} />
                            <Checkbox label="SiNa an Netzbetreiberin / ESTI" className="mt-1" checked={verteilerNetz} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Netzbetreiberin */}
            <div style={{ borderTop: '4px solid #bbb', paddingTop: '2px', marginTop: '10px', marginLeft: '35px' }}>
                <div className="flex justify-between items-start">
                    <div>
                        <h3 style={{ fontWeight: 'bold', fontSize: '12px' }}>Netzbetreiberin / ESTI</h3>
                        <div className="flex items-baseline mt-4">
                            <span>Eingang am</span>
                            <div style={{ 
                                width: '140px', 
                                marginLeft: '25px',
                                position: 'relative',
                                top: '5px',
                                minHeight: '16px'
                            }}>
                                <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                    <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                                </svg>
                                {eingangDatum && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{formatDate(eingangDatum)}</span>}
                            </div>
                        </div>
                    </div>
                    <div className="flex items-baseline" style={{ marginTop: '12.666px' }}>
                        <div className="flex items-baseline" style={{ marginLeft: '-60px' }}>
                            <span style={{ position: 'relative', top: '-3px' }}>Stichproben</span>
                            <div className="ml-2">
                                <Checkbox label="Ja" checked={stichprobeJa} />
                                <Checkbox label="Nein" className="mt-1" checked={stichprobeNein} />
                            </div>
                        </div>
                        <span className="mx-2" style={{ position: 'relative', left: '-4px', top: '-2px', fontSize: '12px', fontWeight: 'bold' }}>→</span>
                        <div style={{ position: 'relative', top: '-3px' }}>
                            <Checkbox label="Keine Mängel festgestellt" checked={keineMaengel} />
                            <Checkbox label="Mängelbericht erstellt" className="mt-1" checked={maengelbericht} />
                            <Checkbox label="Anlage plombiert" className="mt-1" checked={anlagePlombiert} />
                        </div>
                    </div>
                    <div style={{ marginTop: '15px', marginLeft: '5px' }}>
                        <span style={{ position: 'relative', left: '-81px', top: '-2px' }}>Datum, Visum</span>
                        <div style={{ 
                            width: 'calc(100% + 80px)', 
                            marginTop: '13px',
                            position: 'relative',
                            left: '-80px',
                            minHeight: '16px'
                        }}>
                            <svg style={{ position: 'absolute', bottom: '0px', left: '0', right: '0', height: '1px', width: '100%' }}>
                                <line x1="0" y1="0.5" x2="100%" y2="0.5" stroke="#333" strokeWidth="0.1" />
                            </svg>
                            {datumVisum && <span style={{ position: 'absolute', bottom: '-1px', left: 0, fontSize: '10px', maxWidth: '100%', overflow: 'hidden', whiteSpace: 'nowrap' }}>{formatDate(datumVisum)}</span>}
                        </div>
                    </div>
                </div>
            </div>

            {/* Footer */}
            <footer style={{ position: 'relative', marginLeft: '35px', marginTop: '2px' }}>
                <span style={{ 
                    position: 'absolute',
                    left: '-15px',
                    bottom: '-5px',
                    fontSize: '10px'
                }}>Si Na Version 2002.01</span>
                <div style={{ 
                    textAlign: 'center',
                    fontSize: '10px',
                    marginTop: '30px'
                }}>Pro Anlage (Zählerstromkreis) ein Sicherheitsnachweis</div>
            </footer>
        </div>
    );
};

export default SinaForm;
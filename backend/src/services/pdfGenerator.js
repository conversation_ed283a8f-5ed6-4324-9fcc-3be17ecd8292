// Enable Babel transpilation for JSX
require('@babel/register');

const React = require('react');
const ReactDOMServer = require('react-dom/server');
const puppeteer = require('puppeteer');

// Import our SiNaForm component
const SiNaForm = require('../components/SiNaForm-ssr.js').default;

class SiNaPDFGenerator {
  constructor() {
    // React SSR + Puppeteer for PDF generation
    console.log('SiNa PDF Generator initialized');
  }

  async generatePDF(formData) {
    try {
      console.log('Starting PDF generation with form data...');
      
      // 1. Create React element with form data
      const sinaFormElement = React.createElement(SiNaForm, { data: formData });
      
      // 2. Render to HTML string
      const htmlContent = ReactDOMServer.renderToStaticMarkup(sinaFormElement);
      
      // 3. Create complete HTML document
      const fullHtml = this.createHTMLDocument(htmlContent);

      // 4. Launch Puppeteer and generate PDF
      const pdfBuffer = await this.convertHTMLToPDF(fullHtml);
      
      console.log('PDF generated successfully, size:', pdfBuffer.length, 'bytes');
      return pdfBuffer;
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error(`PDF generation failed: ${error.message}`);
    }
  }

  createHTMLDocument(htmlContent) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SiNa Form</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            margin: 0; 
            font-family: Arial, sans-serif;
            background: white;
        }
        @page { 
            size: A4; 
            margin: 0; 
        }
        @media print { 
            body { margin: 0; }
            .A4Page { 
                transform: none !important; 
                box-shadow: none !important;
                width: 210mm !important;
                height: 297mm !important;
            }
        }
        /* Grid and Flex utilities */
        .grid { display: grid; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        .gap-x-8 { column-gap: 2rem; }
        .gap-x-7 { column-gap: 1.75rem; }
        .gap-x-6 { column-gap: 1.5rem; }
        .gap-x-5 { column-gap: 1.25rem; }
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .flex-grow { flex-grow: 1; }
        .items-start { align-items: flex-start; }
        .items-end { align-items: flex-end; }
        .items-center { align-items: center; }
        .items-baseline { align-items: baseline; }
        .justify-between { justify-content: space-between; }
        .justify-end { justify-content: flex-end; }
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .mt-8 { margin-top: 2rem; }
        .mr-4 { margin-right: 1rem; }
        .ml-2 { margin-left: 0.5rem; }
        .ml-4 { margin-left: 1rem; }
        .ml-8 { margin-left: 2rem; }
        .mx-4 { margin-left: 1rem; margin-right: 1rem; }
        .pb-2 { padding-bottom: 0.5rem; }
        .pt-8 { padding-top: 2rem; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .text-xs { font-size: 10px; }
        .text-lg { font-size: 1.125rem; }
        .font-bold { font-weight: bold; }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
  }

  async convertHTMLToPDF(fullHtml) {
    let browser = null;
    try {
      // Launch browser
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ],
        protocolTimeout: 60000, // 60 seconds timeout
        timeout: 60000 // 60 seconds timeout for launch
      });
      
      const page = await browser.newPage();

      // Set page timeout
      page.setDefaultTimeout(60000); // 60 seconds
      page.setDefaultNavigationTimeout(60000); // 60 seconds

      // Set content
      await page.setContent(fullHtml, {
        waitUntil: 'networkidle0',
        timeout: 60000 // 60 seconds timeout for content loading
      });
      
      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0mm',
          right: '0mm',
          bottom: '0mm',
          left: '0mm'
        },
        timeout: 60000 // 60 seconds timeout for PDF generation
      });
      
      return pdfBuffer;
      
    } finally {
      if (browser) {
        try {
          await browser.close();
        } catch (closeError) {
          console.warn('Warning: Error closing browser:', closeError.message);
        }
      }
    }
  }

  // Helper method to get default filename
  getDefaultFilename(formData) {
    const timestamp = new Date().toISOString().slice(0, 10);
    const docNumber = (formData?.documentNumber || 'untitled')
      .replace(/[^\w\-_\s]/g, '') // Remove special chars except word chars, hyphens, underscores, spaces
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .substring(0, 50); // Limit length
    return `SiNa_Report_${docNumber}_${timestamp}.pdf`;
  }
}

module.exports = SiNaPDFGenerator;
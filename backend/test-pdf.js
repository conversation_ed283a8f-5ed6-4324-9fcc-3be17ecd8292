// Enable Babel transpilation for JSX
require('@babel/register');

const React = require('react');
const ReactDOMServer = require('react-dom/server');
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Import our SiNaForm component
const SiNaForm = require('./src/components/SiNaForm-ssr.js').default;

// Mock data for testing
const mockData = {
    // Document info
    documentNumber: 'SN-2025-001',
    pageNumber: '1',
    totalPages: '2',
    
    // Owner (Eigentümer) - Testing with very long text
    ownerName1: 'Max Mustermann AG mit einem sehr sehr langen Namen der normalerweise überlaufen würde',
    ownerName2: 'Geschäftsführung und Verwaltungsrat mit zusätzlichen Titeln die sehr lang sind',
    ownerStreet: 'Bahnhofstrasse 123, Gebäude A, Etage 5, <PERSON>immer 501, Postfach 12345',
    ownerCity: '8001 Zürich, Schweiz, Europa mit einer sehr langen Adresse die normalerweise überlaufen würde',
    ownerPhone: '+41 44 123 45 67 (sehr lange Telefonnummer mit Notizen)',
    
    // Administration (Verwaltung)
    adminName1: 'Immobilien Verwaltung GmbH',
    adminName2: 'Herr Schmidt',
    adminStreet: 'Limmatquai 45',
    adminCity: '8001 Zürich',
    adminPhone: '+41 44 987 65 43',
    
    // Electrical Installer (Elektroinstallateur)
    installerAuthNumber: '12345',
    installerName1: 'ElektroTech AG',
    installerName2: 'Beat Meier',
    installerStreet: 'Industriestrasse 78',
    installerCity: '8050 Zürich',
    installerPhone: '+41 44 555 66 77',
    
    // Control Body (Kontrollorgan)
    controlAuthNumber: 'K-6789',
    controlName1: 'Kontroll Service GmbH',
    controlName2: 'Peter Müller',
    controlStreet: 'Kontrollweg 12',
    controlCity: '8052 Zürich',
    controlPhone: '+41 44 333 44 55',
    
    // Installation Location
    locationStreet: 'Teststrasse 999',
    locationCity: '8001 Zürich',
    buildingType: 'Wohngebäude',
    objectNumber: 'OBJ-2025-001',
    stockwerkLage: '2. OG',
    instAnzeigeNumber: 'INS-123456',
    instAnzeigeDate: '15.01.2025',
    buildingPart: true,
    buildingPartText: 'Keller und Dachboden',
    zev: true,
    zevText: 'Eigenverbrauchsgemeinschaft',
    
    // Controls
    kontrolleSchlusskontrolle: true,
    kontrolleAbnahmekontrolle: false,
    kontrollePeriodische: true,
    kontrolleExtraText1: 'Zusätzliche Kontrollinfo die lang sein kann',
    kontrolleExtraText2: '',
    datumSK: '27.07.2025',
    datumAKPK: '27.07.2025',
    
    // Control Periods
    period1Jahr: false,
    period3Jahre: false,
    period5Jahre: true,
    period5JahreSchIII: false,
    period10Jahre: false,
    period20Jahre: false,
    
    // Control Scope
    scopeNeuanlage: true,
    scopeErweiterung: false,
    scopeAenderung: false,
    scopeTemporaere: false,
    scopeSpezial: true,
    spezialinstText: 'Solar Anlage mit Photovoltaik-Modulen auf dem Dach, Wechselrichter im Keller, Energiespeicher-System mit Lithium-Ionen-Batterien, Smart Home Integration und Überwachungssystem für optimale Energieausbeute und automatische Steuerung der Anlage. DAHA SONRA DENEMEK icin bunlari yazmaya basladim sadece denemek istiyordum.',
    
    // Technical data
    schutzTNS: true,
    schutzTNC: false,
    schutzTNCS: false,
    schutzSchIII: true,
    schIIIText: 'SELV 12V',
    anlageteil: 'Hauptverteiler',
    anschlussStrom: '63',
    
    // Table data - Testing with very long text
    zaehlerNr: 'Z-123456789-SEHR-LANGE-ZAEHLERNUMMER-DIE-NORMALERWEISE-UEBERLAUFEN-WUERDE',
    stromkundeNutzung: 'Wohnungsbeleuchtung und Steckdosen im gesamten Gebäude mit zusätzlichen Notizen',
    artCharakteristik: 'B16/30mA mit sehr langen technischen Spezifikationen und Details',
    stromIN: '16',
    stromKAnfang: '1200',
    stromKEnde: '850',
    stromL: 'L1-PE',
    widerstandISO: '>200',
    
    // Signatures
    installerKontrollberechtigter: 'Beat Meier',
    installerUnterschriftsberechtigter: 'Hans Weber',
    installerDatum1: '27.07.2025',
    installerDatum2: '27.07.2025',
    controlKontrollberechtigter: 'Peter Müller',
    controlUnterschriftsberechtigter: 'Anna Keller',
    controlDatum1: '27.07.2025',
    controlDatum2: '27.07.2025',
    
    // Attachments
    beilageMessProtokoll: true,
    beilagePhotovoltaik: false,
    beilageSonstige: true,
    beilageSonstigeText: 'Zusätzliche Dokumentation und Pläne',
    plombenEntfernt: false,
    verteilerEigentuemer: true,
    verteilerNetz: true,

    // Additional text fields
    anlageStromkreisText: 'Hauptverteilung mit Unterverteilungen',
    
    // Final section
    eingangDatum: '27.07.2025',
    stichprobeJa: false,
    stichprobeNein: true,
    keineMaengel: true,
    maengelbericht: false,
    anlagePlombiert: true,
    datumVisum: '28.07.2025',
    
    // Additional signature data for the form
    installerKontrollberechtigterName: 'Beat Meier',
    installerUnterschriftsberechtigterName: 'Hans Weber',
    controlKontrollberechtigterName: 'Peter Müller',
    controlUnterschriftsberechtigterName: 'Anna Keller'
};

async function generatePDF() {
    try {
        console.log('Starting PDF generation test...');
        
        // Create React element with mock data
        const sinaFormElement = React.createElement(SiNaForm, { data: mockData });
        
        // Render to HTML string
        const htmlContent = ReactDOMServer.renderToStaticMarkup(sinaFormElement);
        
        // Create complete HTML document
        const fullHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SiNa Form Test</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            margin: 0; 
            font-family: Arial, sans-serif;
            background: white;
        }
        @page { 
            size: A4; 
            margin: 0; 
        }
        @media print { 
            body { margin: 0; }
            .A4Page { 
                transform: none !important; 
                box-shadow: none !important;
                width: 210mm !important;
                height: 297mm !important;
            }
        }
        /* Grid and Flex utilities */
        .grid { display: grid; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
        .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        .gap-x-8 { column-gap: 2rem; }
        .gap-x-7 { column-gap: 1.75rem; }
        .gap-x-6 { column-gap: 1.5rem; }
        .gap-x-5 { column-gap: 1.25rem; }
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .flex-grow { flex-grow: 1; }
        .items-start { align-items: flex-start; }
        .items-end { align-items: flex-end; }
        .items-center { align-items: center; }
        .items-baseline { align-items: baseline; }
        .justify-between { justify-content: space-between; }
        .justify-end { justify-content: flex-end; }
        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .mt-8 { margin-top: 2rem; }
        .mr-4 { margin-right: 1rem; }
        .ml-2 { margin-left: 0.5rem; }
        .ml-4 { margin-left: 1rem; }
        .ml-8 { margin-left: 2rem; }
        .mx-4 { margin-left: 1rem; margin-right: 1rem; }
        .pb-2 { padding-bottom: 0.5rem; }
        .pt-8 { padding-top: 2rem; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .text-xs { font-size: 10px; }
        .text-lg { font-size: 1.125rem; }
        .font-bold { font-weight: bold; }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

        // Save HTML file for debugging
        const htmlPath = path.join(__dirname, 'test-output.html');
        fs.writeFileSync(htmlPath, fullHtml);
        console.log('HTML file saved to:', htmlPath);
        
        // Launch Puppeteer
        console.log('Launching browser...');
        const browser = await puppeteer.launch({ 
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Set content (base64 images are embedded, no need for asset serving)
        await page.setContent(fullHtml, { waitUntil: 'networkidle0' });
        
        // Generate PDF
        console.log('Generating PDF...');
        const pdfPath = path.join(__dirname, 'test-sina-form.pdf');
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0mm',
                right: '0mm',
                bottom: '0mm',
                left: '0mm'
            }
        });
        
        await browser.close();
        
        console.log('PDF generated successfully!');
        console.log('PDF saved to:', pdfPath);
        console.log('File size:', fs.statSync(pdfPath).size, 'bytes');
        
        return pdfPath;
        
    } catch (error) {
        console.error('Error generating PDF:', error);
        throw error;
    }
}

// Run the test
if (require.main === module) {
    generatePDF()
        .then(pdfPath => {
            console.log('\n✅ PDF Generation Test Completed Successfully!');
            console.log('📄 PDF Location:', pdfPath);
        })
        .catch(error => {
            console.error('\n❌ PDF Generation Test Failed:', error.message);
            process.exit(1);
        });
}

module.exports = { generatePDF, mockData };
{"title": "SiNa Report Generator - Prototype Version", "description": "Simplified prototype with 15-20 representative fields for capability demonstration", "formFields": [{"id": "owner_name", "labelDE": "Name des Eigentümers", "labelEN": "Owner Name", "type": "text", "required": true, "section": "Installation Owner", "validation": {"minLength": 2, "maxLength": 50, "pattern": "^[A-Za-zÄÖÜäöüß\\s-]+$"}}, {"id": "owner_address", "labelDE": "<PERSON><PERSON><PERSON>", "labelEN": "Address", "type": "text", "required": true, "section": "Installation Owner", "placeholder": "Strasse Nr., PLZ Ort"}, {"id": "owner_phone", "labelDE": "Telefon", "labelEN": "Phone", "type": "tel", "required": false, "section": "Installation Owner", "validation": {"pattern": "^[+41]*[\\d\\s\\-\\(\\)]+$"}}, {"id": "inspector_name", "labelDE": "Inspektor", "labelEN": "Inspector", "type": "dropdown", "required": true, "section": "Electrical Installer", "options": [{"value": "mueller_hans", "labelDE": "<PERSON>", "labelEN": "<PERSON>"}, {"value": "weber_anna", "labelDE": "<PERSON>", "labelEN": "<PERSON>"}, {"value": "schneider_peter", "labelDE": "<PERSON>", "labelEN": "<PERSON>"}, {"value": "fischer_maria", "labelDE": "<PERSON>", "labelEN": "<PERSON>"}]}, {"id": "installer_auth_number", "labelDE": "Bewilligungs-Nr.", "labelEN": "Authorization Number", "type": "text", "required": true, "section": "Electrical Installer", "placeholder": "z.B. CH-12345", "validation": {"pattern": "^[A-Z]{2}-[0-9]{4,6}$"}}, {"id": "installer_company", "labelDE": "Firma/<PERSON>resse", "labelEN": "Company/Address", "type": "text", "required": true, "section": "Electrical Installer"}, {"id": "installation_address", "labelDE": "Installationsort", "labelEN": "Installation Address", "type": "text", "required": true, "section": "Installation Location"}, {"id": "building_type", "labelDE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelEN": "Building Type", "type": "dropdown", "required": true, "section": "Installation Location", "options": [{"value": "residential", "labelDE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelEN": "Residential Building"}, {"value": "commercial", "labelDE": "Gewerbegebäude", "labelEN": "Commercial Building"}, {"value": "industrial", "labelDE": "Industriegebäude", "labelEN": "Industrial Building"}, {"value": "public", "labelDE": "Öffentliches Gebäude", "labelEN": "Public Building"}]}, {"id": "object_number", "labelDE": "Objekt Nr.", "labelEN": "Object Number", "type": "text", "required": false, "section": "Installation Location"}, {"id": "control_type", "labelDE": "<PERSON><PERSON><PERSON><PERSON>", "labelEN": "Control Type", "type": "dropdown", "required": true, "section": "Control Information", "options": [{"value": "final_inspection", "labelDE": "Schlusskontrolle", "labelEN": "Final Inspection"}, {"value": "acceptance_control", "labelDE": "Abnahmekon<PERSON>lle", "labelEN": "Acceptance Control"}, {"value": "periodic_control", "labelDE": "Periodische Kontrolle", "labelEN": "Periodic Control"}]}, {"id": "control_date", "labelDE": "Kontrollda<PERSON>", "labelEN": "Control Date", "type": "date", "required": true, "section": "Control Information"}, {"id": "control_scope", "labelDE": "Kontrollumfang", "labelEN": "Control Scope", "type": "textarea", "required": false, "section": "Control Information", "placeholder": "Beschreibung der durchgeführten Arbeiten..."}, {"id": "protection_system", "labelDE": "Schutz-System", "labelEN": "Protection System", "type": "dropdown", "required": true, "section": "Technical Information", "options": [{"value": "TN-S", "labelDE": "TN-S System", "labelEN": "TN-S System"}, {"value": "TN-C", "labelDE": "TN-C System", "labelEN": "TN-C System"}, {"value": "TN-C-S", "labelDE": "TN-C-S System", "labelEN": "TN-C-S System"}, {"value": "TT", "labelDE": "TT System", "labelEN": "TT System"}, {"value": "IT", "labelDE": "IT System", "labelEN": "IT System"}]}, {"id": "rated_current", "labelDE": "<PERSON><PERSON>nstrom [A]", "labelEN": "Rated Current [A]", "type": "number", "required": false, "section": "Technical Information", "validation": {"min": 1, "max": 1000, "step": 1}}, {"id": "deficiencies", "labelDE": "Mängel/Bemerkungen", "labelEN": "Deficiencies/Comments", "type": "textarea", "required": false, "section": "Additional Information", "placeholder": "Festgestellte Mängel oder zusätzliche Bemerkungen..."}, {"id": "inspector_signature", "labelDE": "Inspektor Unterschrift", "labelEN": "Inspector Signature", "type": "text", "required": true, "section": "Signatures", "placeholder": "Unterschrift des Inspektors"}, {"id": "signature_date", "labelDE": "Datum", "labelEN": "Date", "type": "date", "required": true, "section": "Signatures"}], "sections": [{"id": "owner", "titleDE": "Eigentümer der Installation", "titleEN": "Installation Owner"}, {"id": "installer", "titleDE": "Elektroinstallateur", "titleEN": "Electrical Installer"}, {"id": "location", "titleDE": "Ort der Installation", "titleEN": "Installation Location"}, {"id": "control", "titleDE": "Kontrollinformationen", "titleEN": "Control Information"}, {"id": "technical", "titleDE": "Technische Angaben", "titleEN": "Technical Information"}, {"id": "additional", "titleDE": "Zusätzliche Informationen", "titleEN": "Additional Information"}, {"id": "signatures", "titleDE": "Unterschriften", "titleEN": "Signatures"}]}
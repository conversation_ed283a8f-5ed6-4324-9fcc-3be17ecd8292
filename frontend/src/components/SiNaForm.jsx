import React, { useState } from 'react';
import Section from './Section';

// Character limits for form fields
const FIELD_LIMITS = {
  // Document header - reduced for Nr. field
  documentNumber: 25,
  
  // Contact info - increased based on visual inspection
  ownerName1: 30,
  ownerName2: 30,
  ownerStreet: 30,  // Same as Name fields
  ownerCity: 30,
  ownerPhone: 18,
  
  adminName1: 30,
  adminName2: 30,
  adminStreet: 30,  // Same as Name fields
  adminCity: 30,
  adminPhone: 18,
  
  // Installer info - increased based on visual inspection
  installerAuthNumber: 12,
  installerName1: 30,
  installerName2: 30,
  installerStreet: 30,  // Same as Name fields
  installerCity: 30,
  installerPhone: 18,
  
  // Control body - increased based on visual inspection
  controlAuthNumber: 9,
  controlName1: 30,
  controlName2: 30,
  controlStreet: 30,  // Same as Name fields
  controlCity: 30,
  controlPhone: 18,
  
  // Installation location - reduced based on visual inspection  
  installationLocation: 25,  // Main installation location field  
  locationStreet: 30,  // Shortened for "Strasse, Nr." under Eigentümer
  locationCity: 30,
  buildingType: 30,    // Increased by 5 characters from 35
  objectNumber: 15,
  stockwerkLage: 15,
  instAnzeigeNumber: 25,
  buildingPartText: 15,  // Shortened for Gebäudeteil
  zevText: 15,           // Shortened for ZEV
  
  // Controls
  kontrolleExtraText1: 30,
  spezialinstText: 250,  // Increased to 80
  
  // Technical data - shortened based on visual inspection
  schIIIText: 30,         // Further shortened for Sch III
  anlageteil: 30,         // Further shortened for Anlageteil
  anschlussStrom: 8,      // Shortened for Anschlussüberstromunterbrecher IN
  
  // Table data - increased based on visual inspection
  zaehlerNr: 30,
  stromkundeNutzung: 30,
  artCharakteristik: 25,
  stromIN: 10,
  stromKAnfang: 10,
  stromKEnde: 10,
  widerstandISO: 10,
  
  // Signatures - increased based on visual inspection
  installerKontrollberechtigter: 45,
  installerUnterschriftsberechtigter: 45,
  controlKontrollberechtigter: 45,
  controlUnterschriftsberechtigter: 45,
  
  // Final section
  eingangDatum: 15,
  datumVisum: 25,

  // New text fields
  beilageSonstigeText: 30,
  anlageStromkreisText: 30
};

// Helper function to get common input props with maxLength
const getInputProps = (name, formData, handleInputChange, isFieldMissing = null, additionalProps = {}) => {
  const baseProps = {
    name,
    value: formData[name] || '',
    onChange: handleInputChange,
    maxLength: FIELD_LIMITS[name] || undefined,
    ...additionalProps
  };
  
  if (isFieldMissing && isFieldMissing(name)) {
    baseProps.className = (baseProps.className || '') + ' missing-field';
  }
  
  return baseProps;
};

const SiNaForm = () => {
  const [formData, setFormData] = useState({
    // Document info
    documentNumber: '',
    pageNumber: '1',
    totalPages: '2',
    
    // Owner (Eigentümer)
    ownerName1: '',
    ownerName2: '',
    ownerStreet: '',
    ownerCity: '',
    ownerPhone: '',
    
    // Administration (Verwaltung)
    adminName1: '',
    adminName2: '',
    adminStreet: '',
    adminCity: '',
    adminPhone: '',
    
    // Electrical Installer (Elektroinstallateur)
    installerAuthNumber: '',
    installerName1: '',
    installerName2: '',
    installerStreet: '',
    installerCity: '',
    installerPhone: '',
    
    // Control Body (Kontrollorgan)
    controlAuthNumber: '',
    controlName1: '',
    controlName2: '',
    controlStreet: '',
    controlCity: '',
    controlPhone: '',
    
    // Installation Location
    installationLocation: '',
    locationStreet: '',
    locationCity: '',
    buildingType: '',
    objectNumber: '',
    stockwerkLage: '',
    instAnzeigeNumber: '',
    instAnzeigeDate: '',
    buildingPart: false,
    buildingPartText: '',
    zev: false,
    zevText: '',
    
    // Controls
    kontrolleSchlusskontrolle: false,
    kontrolleAbnahmekontrolle: false,
    kontrollePeriodische: false,
    kontrolleExtraText1: '',
    kontrolleExtraText2: '',
    datumSK: '',
    datumAKPK: '',
    
    // Control Periods
    period1Jahr: false,
    period3Jahre: false,
    period5Jahre: false,
    period5JahreSchIII: false,
    period10Jahre: false,
    period20Jahre: false,
    
    // Control Scope
    scopeNeuanlage: false,
    scopeErweiterung: false,
    scopeAenderung: false,
    scopeTemporaere: false,
    scopeSpezial: false,
    spezialinstText: '',
    
    // Technical data
    schutzTNS: false,
    schutzTNC: false,
    schutzTNCS: false,
    schutzSchIII: false,
    schIIIText: '',
    anlageteil: '',
    anschlussStrom: '',
    
    // Table data
    zaehlerNr: '',
    stromkundeNutzung: '',
    artCharakteristik: '',
    stromIN: '',
    stromKAnfang: '',
    stromKEnde: '',
    stromL: '',
    widerstandISO: '',
    
    // Signatures
    installerKontrollberechtigter: '',
    installerUnterschriftsberechtigter: '',
    installerDatum1: '',
    installerDatum2: '',
    controlKontrollberechtigter: '',
    controlUnterschriftsberechtigter: '',
    controlDatum1: '',
    controlDatum2: '',
    
    // Attachments
    beilageMessProtokoll: false,
    beilagePhotovoltaik: false,
    beilageSonstige: false,
    beilageSonstigeText: '',
    plombenEntfernt: false,
    verteilerEigentuemer: false,
    verteilerNetz: false,

    // Additional text fields
    anlageStromkreisText: '',
    
    // Extra controls
    kontrolleExtra: false,
    
    // Final section
    eingangDatum: '',
    stichprobeJa: false,
    stichprobeNein: false,
    keineMaengel: false,
    maengelbericht: false,
    anlagePlombiert: false,
    datumVisum: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [missingFields, setMissingFields] = useState([]);

  const handleKeyDown = (e) => {
    const { name } = e.target;
    const numericFields = ['anschlussStrom', 'stromIN', 'stromKAnfang', 'stromKEnde', 'widerstandISO'];
    
    // Prevent 'e', 'E', '+', '-' in numeric fields
    if (numericFields.includes(name) && ['e', 'E', '+', '-'].includes(e.key)) {
      e.preventDefault();
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Phone number validation - only allow numbers, spaces, +, -, (, )
    const phoneFields = ['ownerPhone', 'adminPhone', 'installerPhone', 'controlPhone'];
    if (phoneFields.includes(name) && type !== 'checkbox') {
      const numericValue = value.replace(/[^\d\s\+\-\(\)]/g, '');
      setFormData(prev => ({
        ...prev,
        [name]: numericValue
      }));
      return;
    }
    
    // Numeric fields validation - only allow numbers and decimal points
    const numericFields = ['anschlussStrom', 'stromIN', 'stromKAnfang', 'stromKEnde', 'widerstandISO'];
    if (numericFields.includes(name) && type !== 'checkbox') {
      const numericValue = value.replace(/[^\d.]/g, '');
      setFormData(prev => ({
        ...prev,
        [name]: numericValue
      }));
      return;
    }
    
    if (type === 'checkbox' && name.startsWith('period')) {
      // For control periods, only allow one selection
      const newData = { ...formData };
      Object.keys(newData).forEach(key => {
        if (key.startsWith('period')) {
          newData[key] = false;
        }
      });
      newData[name] = checked;
      setFormData(newData);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
      
      // Clear field from missing fields if it's now filled
      if (type !== 'checkbox' && value.trim() !== '' && missingFields.includes(name)) {
        setMissingFields(prev => prev.filter(field => field !== name));
      }
    }
  };

  const validateRequiredFields = () => {
    const requiredFields = [
      'ownerName1', 'ownerStreet', 'ownerCity',
      'installerAuthNumber', 'installerName1', 'installerStreet', 'installerCity',
      'controlAuthNumber', 'controlName1', 'controlStreet', 'controlCity',
      'locationStreet', 'locationCity', 'anschlussStrom',
      'zaehlerNr', 'stromkundeNutzung', 'stromIN', 'stromKAnfang', 'stromKEnde', 'widerstandISO',
      'installerKontrollberechtigter', 'installerUnterschriftsberechtigter',
      'controlKontrollberechtigter', 'controlUnterschriftsberechtigter'
    ];

    return requiredFields.filter(field => {
      const value = formData[field];
      return !value || value.toString().trim() === '';
    });
  };

  const isFieldMissing = (fieldName) => {
    return missingFields.includes(fieldName);
  };

  const handleGeneratePDF = async () => {
    setIsLoading(true);
    setMessage('');
    
    // Validate required fields
    const currentMissingFields = validateRequiredFields();
    setMissingFields(currentMissingFields);
    
    if (currentMissingFields.length > 0) {
      setMessage(`❌ Bitte füllen Sie alle markierten Pflichtfelder aus.`);
      setIsLoading(false);
      
      // Scroll to first missing field smoothly
      setTimeout(() => {
        const firstMissingField = currentMissingFields[0];
        
        // First, find which section contains this field by mapping field names to sections
        const fieldToSectionMap = {
          'ownerName1': 'Eigentümer der Installation',
          'ownerStreet': 'Eigentümer der Installation', 
          'ownerCity': 'Eigentümer der Installation',
          'adminName1': 'Verwaltung',
          'adminName2': 'Verwaltung',
          'adminStreet': 'Verwaltung',
          'adminCity': 'Verwaltung',
          'installerAuthNumber': 'Elektroinstallateur',
          'installerName1': 'Elektroinstallateur',
          'installerStreet': 'Elektroinstallateur',
          'installerCity': 'Elektroinstallateur',
          'controlAuthNumber': 'Unabhängiges Kontrollorgan',
          'controlName1': 'Unabhängiges Kontrollorgan',
          'controlStreet': 'Unabhängiges Kontrollorgan',
          'controlCity': 'Unabhängiges Kontrollorgan',
          'locationStreet': 'Ort der Installation',
          'locationCity': 'Ort der Installation',
          'anschlussStrom': 'Technische Angaben',
          'zaehlerNr': 'Anlage / Stromkreis',
          'stromkundeNutzung': 'Anlage / Stromkreis',
          'stromIN': 'Anlage / Stromkreis',
          'stromKAnfang': 'Anlage / Stromkreis',
          'stromKEnde': 'Anlage / Stromkreis',
          'widerstandISO': 'Anlage / Stromkreis',
          'installerKontrollberechtigter': 'Unterschriften',
          'installerUnterschriftsberechtigter': 'Unterschriften',
          'controlKontrollberechtigter': 'Unterschriften',
          'controlUnterschriftsberechtigter': 'Unterschriften'
        };
        
        const sectionTitle = fieldToSectionMap[firstMissingField];
        if (sectionTitle) {
          // Find the section by title
          const sectionElement = document.querySelector(`[data-section="${sectionTitle}"]`);
          if (sectionElement) {
            // Check if section is collapsed
            const sectionContent = sectionElement.querySelector('.section-content');
            const sectionHeader = sectionElement.querySelector('.section-header');
            
            if (!sectionContent && sectionHeader) {
              // Section is collapsed, expand it first
              sectionHeader.click();
              
              // Wait for React to re-render and add the content to DOM
              setTimeout(() => {
                const fieldElement = document.querySelector(`input[name="${firstMissingField}"]`);
                if (fieldElement) {
                  sectionElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
                  setTimeout(() => {
                    fieldElement.focus();
                  }, 500);
                }
              }, 100);
            } else {
              // Section is already expanded
              const fieldElement = document.querySelector(`input[name="${firstMissingField}"]`);
              if (fieldElement) {
                sectionElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
                setTimeout(() => {
                  fieldElement.focus();
                }, 500);
              }
            }
          }
        } else {
          // Fallback: try to find the field directly
          const fieldElement = document.querySelector(`input[name="${firstMissingField}"]`);
          if (fieldElement) {
            fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
            setTimeout(() => {
              fieldElement.focus();
            }, 500);
          }
        }
      }, 100);
      
      return;
    }
    
    try {
      const response = await fetch('http://localhost:3001/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle PDF download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `SiNa_Report_${formData.documentNumber || Date.now()}.pdf`;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setMessage('✅ PDF erfolgreich generiert und heruntergeladen!');
      setMissingFields([]); // Clear missing fields on success
    } catch (error) {
      console.error('Error generating PDF:', error);
      setMessage('❌ Fehler beim Generieren der PDF: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="sections">
      {/* Status Message at the top */}
      {message && (
        <div style={{ 
          textAlign: 'center', 
          marginBottom: '2rem', 
          padding: '1rem', 
          backgroundColor: message.includes('✅') ? '#d4edda' : '#f8d7da',
          color: message.includes('✅') ? '#155724' : '#721c24',
          borderRadius: '8px',
          fontWeight: 'bold'
        }}>
          {message}
        </div>
      )}
      
      <Section title="Allgemeine Informationen" icon="📝">
        <div className="form-field">
          <label>Nr.:</label>
          <input {...getInputProps('documentNumber', formData, handleInputChange)} />
        </div>
      </Section>

      <Section title="Eigentümer der Installation" icon="👤" expanded={true}>
        <div className="form-grid">
          <div className="form-field">
            <label>Name 1 *:</label>
            <input 
              name="ownerName1"
              value={formData.ownerName1}
              onChange={handleInputChange}
              className={isFieldMissing('ownerName1') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.ownerName1}
            />
          </div>
          <div className="form-field">
            <label>Name 2:</label>
            <input 
              name="ownerName2"
              value={formData.ownerName2}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.ownerName2}
            />
          </div>
          <div className="form-field">
            <label>Strasse, Nr. *:</label>
            <input 
              name="ownerStreet"
              value={formData.ownerStreet}
              onChange={handleInputChange}
              className={isFieldMissing('ownerStreet') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.ownerStreet}
            />
          </div>
          <div className="form-field">
            <label>PLZ, Ort *:</label>
            <input 
              name="ownerCity"
              value={formData.ownerCity}
              onChange={handleInputChange}
              className={isFieldMissing('ownerCity') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.ownerCity}
            />
          </div>
          <div className="form-field">
            <label>Tel. Nr.:</label>
            <input
              name="ownerPhone"
              value={formData.ownerPhone}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.ownerPhone}
              placeholder="+41 XX XXX XX XX"
            />
          </div>
        </div>
      </Section>

      <Section title="Verwaltung" icon="🏢">
        <div className="form-grid">
          <div className="form-field">
            <label>Name 1:</label>
            <input 
              name="adminName1"
              value={formData.adminName1}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.adminName1}
            />
          </div>
          <div className="form-field">
            <label>Name 2:</label>
            <input 
              name="adminName2"
              value={formData.adminName2}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.adminName2}
            />
          </div>
          <div className="form-field">
            <label>Strasse, Nr.:</label>
            <input 
              name="adminStreet"
              value={formData.adminStreet}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.adminStreet}
            />
          </div>
          <div className="form-field">
            <label>PLZ, Ort:</label>
            <input 
              name="adminCity"
              value={formData.adminCity}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.adminCity}
            />
          </div>
          <div className="form-field">
            <label>Tel. Nr.:</label>
            <input
              name="adminPhone"
              value={formData.adminPhone}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.adminPhone}
              placeholder="+41 XX XXX XX XX"
            />
          </div>
        </div>
      </Section>

      <Section title="Elektroinstallateur" icon="⚡">
        <div className="form-grid">
          <div className="form-field">
            <label>Bew.-Nr. I- *:</label>
            <input {...getInputProps('installerAuthNumber', formData, handleInputChange, isFieldMissing)} />
          </div>
          <div className="form-field">
            <label>Name 1 *:</label>
            <input 
              name="installerName1"
              value={formData.installerName1}
              onChange={handleInputChange}
              className={isFieldMissing('installerName1') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.installerName1}
            />
          </div>
          <div className="form-field">
            <label>Name 2:</label>
            <input 
              name="installerName2"
              value={formData.installerName2}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.installerName2}
            />
          </div>
          <div className="form-field">
            <label>Strasse, Nr. *:</label>
            <input 
              name="installerStreet"
              value={formData.installerStreet}
              onChange={handleInputChange}
              className={isFieldMissing('installerStreet') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.installerStreet}
            />
          </div>
          <div className="form-field">
            <label>PLZ, Ort *:</label>
            <input 
              name="installerCity"
              value={formData.installerCity}
              onChange={handleInputChange}
              className={isFieldMissing('installerCity') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.installerCity}
            />
          </div>
          <div className="form-field">
            <label>Tel. Nr.:</label>
            <input
              name="installerPhone"
              value={formData.installerPhone}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.installerPhone}
              placeholder="+41 XX XXX XX XX"
            />
          </div>
        </div>
      </Section>

      <Section title="Unabhängiges Kontrollorgan" icon="🛡️">
        <div className="form-grid">
          <div className="form-field">
            <label>Bew.-Nr. K- *:</label>
            <input {...getInputProps('controlAuthNumber', formData, handleInputChange, isFieldMissing)} />
          </div>
          <div className="form-field">
            <label>Name 1 *:</label>
            <input 
              name="controlName1"
              value={formData.controlName1}
              onChange={handleInputChange}
              className={isFieldMissing('controlName1') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.controlName1}
            />
          </div>
          <div className="form-field">
            <label>Name 2:</label>
            <input 
              name="controlName2"
              value={formData.controlName2}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.controlName2}
            />
          </div>
          <div className="form-field">
            <label>Strasse, Nr. *:</label>
            <input 
              name="controlStreet"
              value={formData.controlStreet}
              onChange={handleInputChange}
              className={isFieldMissing('controlStreet') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.controlStreet}
            />
          </div>
          <div className="form-field">
            <label>PLZ, Ort *:</label>
            <input 
              name="controlCity"
              value={formData.controlCity}
              onChange={handleInputChange}
              className={isFieldMissing('controlCity') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.controlCity}
            />
          </div>
          <div className="form-field">
            <label>Tel. Nr.:</label>
            <input
              name="controlPhone"
              value={formData.controlPhone}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.controlPhone}
              placeholder="+41 XX XXX XX XX"
            />
          </div>
        </div>
      </Section>

      <Section title="Ort der Installation" icon="📍">
        <div className="form-field" style={{ marginBottom: '1rem' }}>
          <label style={{ fontWeight: 'bold' }}>Ort der Installation:</label>
          <input 
            name="installationLocation"
            value={formData.installationLocation}
            onChange={handleInputChange}
            maxLength={FIELD_LIMITS.installationLocation}
          />
        </div>
        <div className="form-grid">
          <div className="form-field">
            <label>Strasse, Nr. *:</label>
            <input 
              name="locationStreet"
              value={formData.locationStreet}
              onChange={handleInputChange}
              className={isFieldMissing('locationStreet') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.locationStreet}
            />
          </div>
          <div className="form-field">
            <label>PLZ, Ort *:</label>
            <input 
              name="locationCity"
              value={formData.locationCity}
              onChange={handleInputChange}
              className={isFieldMissing('locationCity') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.locationCity}
            />
          </div>
          <div className="form-field">
            <label>Gebäudeart:</label>
            <input 
              name="buildingType"
              value={formData.buildingType}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.buildingType}
            />
          </div>
          <div className="form-field">
            <label>Inst.-Anzeige Nr.:</label>
            <input 
              name="instAnzeigeNumber"
              value={formData.instAnzeigeNumber}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.instAnzeigeNumber}
            />
          </div>
        </div>
        <div className="checkbox-group">
          <label>
            <input 
              type="checkbox" 
              name="buildingPart"
              checked={formData.buildingPart}
              onChange={handleInputChange}
            /> 
            Gebäudeteil
          </label>
          {formData.buildingPart && (
            <input 
              name="buildingPartText"
              value={formData.buildingPartText}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.buildingPartText}
            />
          )}
          <label>
            <input 
              type="checkbox" 
              name="zev"
              checked={formData.zev}
              onChange={handleInputChange}
            /> 
            ZEV
          </label>
          {formData.zev && (
            <input 
              name="zevText"
              value={formData.zevText}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.zevText}
            />
          )}
        </div>
      </Section>

      <Section title="Durchgeführte Kontrollen" icon="✅">
        <div className="checkbox-group">
          <h4>Kontrollart:</h4>
          <label>
            <input 
              type="checkbox" 
              name="kontrolleSchlusskontrolle"
              checked={formData.kontrolleSchlusskontrolle}
              onChange={handleInputChange}
            /> 
            Schlusskontrolle SK
          </label>
          <label>
            <input 
              type="checkbox" 
              name="kontrolleAbnahmekontrolle"
              checked={formData.kontrolleAbnahmekontrolle}
              onChange={handleInputChange}
            /> 
            Abnahmekontrolle AK
          </label>
          <label>
            <input 
              type="checkbox" 
              name="kontrollePeriodische"
              checked={formData.kontrollePeriodische}
              onChange={handleInputChange}
            /> 
            Periodische Kontrolle PK
          </label>
          
          {/* Empty checkbox with text field moved from Kontrollperiode */}
          <label>
            <input 
              type="checkbox" 
              name="kontrolleExtra"
              checked={formData.kontrolleExtra || false}
              onChange={handleInputChange}
            /> 
            {/* Empty checkbox */}
          </label>
          {formData.kontrolleExtra && (
            <input 
              name="kontrolleExtraText1"
              value={formData.kontrolleExtraText1}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.kontrolleExtraText1}
            />
          )}
          
          <h4>Kontrollperiode:</h4>
          <div className="dropdown-group">
            <select
              name="kontrollperiode"
              value={
                formData.period1Jahr ? '1Jahr' :
                formData.period3Jahre ? '3Jahre' :
                formData.period5Jahre ? '5Jahre' :
                formData.period5JahreSchIII ? '5JahreSchIII' :
                formData.period10Jahre ? '10Jahre' :
                formData.period20Jahre ? '20Jahre' : ''
              }
              onChange={(e) => {
                const periods = ['period1Jahr', 'period3Jahre', 'period5Jahre', 'period5JahreSchIII', 'period10Jahre', 'period20Jahre'];
                const newData = { ...formData };
                periods.forEach(p => newData[p] = false);
                if (e.target.value) {
                  newData[`period${e.target.value}`] = true;
                }
                setFormData(newData);
              }}
            >
              <option value="">Wählen Sie eine Option</option>
              <option value="1Jahr">1 Jahr</option>
              <option value="3Jahre">3 Jahre</option>
              <option value="5Jahre">5 Jahre</option>
              <option value="5JahreSchIII">5 Jahre (Sch III)</option>
              <option value="10Jahre">10 Jahre</option>
              <option value="20Jahre">20 Jahre</option>
            </select>
          </div>

          <h4>Kontrollumfang:</h4>
          <label>
            <input 
              type="checkbox" 
              name="scopeNeuanlage"
              checked={formData.scopeNeuanlage}
              onChange={handleInputChange}
            /> 
            Neuanlage
          </label>
          <label>
            <input 
              type="checkbox" 
              name="scopeErweiterung"
              checked={formData.scopeErweiterung}
              onChange={handleInputChange}
            /> 
            Erweiterung
          </label>
          <label>
            <input 
              type="checkbox" 
              name="scopeAenderung"
              checked={formData.scopeAenderung}
              onChange={handleInputChange}
            /> 
            Änderung/Umbau
          </label>
          <label>
            <input 
              type="checkbox" 
              name="scopeTemporaere"
              checked={formData.scopeTemporaere}
              onChange={handleInputChange}
            /> 
            Temporäre Anlage
          </label>
          <label>
            <input 
              type="checkbox" 
              name="scopeSpezial"
              checked={formData.scopeSpezial}
              onChange={handleInputChange}
            /> 
            Spezialinstallation
          </label>
          {formData.scopeSpezial && (
            <input 
              name="spezialinstText"
              value={formData.spezialinstText}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.spezialinstText}
            />
          )}
        </div>
        
        <div className="date-picker-group">
          <label>Datum SK:</label>
          <input 
            type="date"
            name="datumSK"
            value={formData.datumSK}
            onChange={handleInputChange}
          />
          <label>Datum AK/PK:</label>
          <input 
            type="date"
            name="datumAKPK"
            value={formData.datumAKPK}
            onChange={handleInputChange}
          />
        </div>
      </Section>

      <Section title="Technische Angaben" icon="⚙️">
        <div className="checkbox-group">
          <h4>Schutz-System:</h4>
          <label>
            <input 
              type="checkbox" 
              name="schutzTNS"
              checked={formData.schutzTNS}
              onChange={handleInputChange}
            /> 
            TN-S
          </label>
          <label>
            <input 
              type="checkbox" 
              name="schutzTNC"
              checked={formData.schutzTNC}
              onChange={handleInputChange}
            /> 
            TN-C
          </label>
          <label>
            <input 
              type="checkbox" 
              name="schutzTNCS"
              checked={formData.schutzTNCS}
              onChange={handleInputChange}
            /> 
            TN-C-S
          </label>
          <label>
            <input 
              type="checkbox" 
              name="schutzSchIII"
              checked={formData.schutzSchIII}
              onChange={handleInputChange}
            /> 
            Sch III
          </label>
          {formData.schutzSchIII && (
            <input 
              name="schIIIText"
              value={formData.schIIIText}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.schIIIText}
            />
          )}
        </div>
        
        <div className="form-grid">
          <div className="form-field">
            <label>Anschlussüberstromschutz IN [A] *:</label>
            <input 
              name="anschlussStrom"
              value={formData.anschlussStrom}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              type="number"
              className={isFieldMissing('anschlussStrom') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.anschlussStrom}
            />
          </div>
          <div className="form-field">
            <label>Anlageteil:</label>
            <input 
              name="anlageteil"
              value={formData.anlageteil}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.anlageteil}
            />
          </div>
        </div>
      </Section>

      <Section title="Anlage / Stromkreis" icon="⚡">
        <div style={{ marginBottom: '1rem', display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Anlage / Stromkreis:</span>
          <input
            name="anlageStromkreisText"
            value={formData.anlageStromkreisText}
            onChange={handleInputChange}
            maxLength={FIELD_LIMITS.anlageStromkreisText}
            style={{
              padding: '0.75rem',
              borderRadius: '8px',
              border: 'none',
              fontSize: '1rem',
              backgroundColor: '#f3f0ff',
              color: '#1a1a1a',
              flexGrow: 1
            }}
            placeholder=""
          />
        </div>
        <div className="form-grid">
          <div className="form-field">
            <label>Zähler Nr. *:</label>
            <input 
              name="zaehlerNr"
              value={formData.zaehlerNr}
              onChange={handleInputChange}
              className={isFieldMissing('zaehlerNr') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.zaehlerNr}
            />
          </div>
          <div className="form-field">
            <label>Stromkunde / Nutzung *:</label>
            <input 
              name="stromkundeNutzung"
              value={formData.stromkundeNutzung}
              onChange={handleInputChange}
              className={isFieldMissing('stromkundeNutzung') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.stromkundeNutzung}
            />
          </div>
          <div className="form-field">
            <label>Art, Charakteristik:</label>
            <input 
              name="artCharakteristik"
              value={formData.artCharakteristik}
              onChange={handleInputChange}
              maxLength={FIELD_LIMITS.artCharakteristik}
            />
          </div>
          <div className="form-field">
            <label>IN [A] *:</label>
            <input 
              name="stromIN"
              value={formData.stromIN}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              type="number"
              className={isFieldMissing('stromIN') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.stromIN}
            />
          </div>
          <div className="form-field">
            <label>IK Anfang L-PE [A] *:</label>
            <input 
              name="stromKAnfang"
              value={formData.stromKAnfang}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              type="number"
              className={isFieldMissing('stromKAnfang') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.stromKAnfang}
            />
          </div>
          <div className="form-field">
            <label>IK Ende L-PE [A] *:</label>
            <input 
              name="stromKEnde"
              value={formData.stromKEnde}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              type="number"
              className={isFieldMissing('stromKEnde') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.stromKEnde}
            />
          </div>
          <div className="form-field">
            <label>R iso [MΩ] *:</label>
            <input 
              name="widerstandISO"
              value={formData.widerstandISO}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              type="number"
              className={isFieldMissing('widerstandISO') ? 'missing-field' : ''}
              maxLength={FIELD_LIMITS.widerstandISO}
            />
          </div>
        </div>
      </Section>

      <Section title="Unterschriften" icon="✍️">
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
          <div>
            <h4>Elektroinstallateur:</h4>
            <div className="form-field">
              <label>Kontrollberechtigter *:</label>
              <input 
                name="installerKontrollberechtigter"
                value={formData.installerKontrollberechtigter}
                onChange={handleInputChange}
                className={isFieldMissing('installerKontrollberechtigter') ? 'missing-field' : ''}
                maxLength={FIELD_LIMITS.installerKontrollberechtigter}
              />
            </div>
            <div className="form-field">
              <label>Unterschriftsberechtigter *:</label>
              <input 
                name="installerUnterschriftsberechtigter"
                value={formData.installerUnterschriftsberechtigter}
                onChange={handleInputChange}
                className={isFieldMissing('installerUnterschriftsberechtigter') ? 'missing-field' : ''}
                maxLength={FIELD_LIMITS.installerUnterschriftsberechtigter}
              />
            </div>
            <div className="form-field">
              <label>Datum:</label>
              <input 
                type="date"
                name="installerDatum1"
                value={formData.installerDatum1}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <div>
            <h4>Kontrollorgan:</h4>
            <div className="form-field">
              <label>Kontrollberechtigter *:</label>
              <input 
                name="controlKontrollberechtigter"
                value={formData.controlKontrollberechtigter}
                onChange={handleInputChange}
                className={isFieldMissing('controlKontrollberechtigter') ? 'missing-field' : ''}
                maxLength={FIELD_LIMITS.controlKontrollberechtigter}
              />
            </div>
            <div className="form-field">
              <label>Unterschriftsberechtigter *:</label>
              <input 
                name="controlUnterschriftsberechtigter"
                value={formData.controlUnterschriftsberechtigter}
                onChange={handleInputChange}
                className={isFieldMissing('controlUnterschriftsberechtigter') ? 'missing-field' : ''}
                maxLength={FIELD_LIMITS.controlUnterschriftsberechtigter}
              />
            </div>
            <div className="form-field">
              <label>Datum:</label>
              <input 
                type="date"
                name="controlDatum1"
                value={formData.controlDatum1}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>
      </Section>

      <Section title="Beilagen" icon="📎">
        <div className="checkbox-group">
          <label>
            <input 
              type="checkbox" 
              name="beilageMessProtokoll"
              checked={formData.beilageMessProtokoll}
              onChange={handleInputChange}
            /> 
            Mess- + Prüfprotokoll
          </label>
          <label>
            <input 
              type="checkbox" 
              name="beilagePhotovoltaik"
              checked={formData.beilagePhotovoltaik}
              onChange={handleInputChange}
            /> 
            Mess- + Prüfprotokoll Photovoltaik
          </label>
          <label style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <input
              type="checkbox"
              name="beilageSonstige"
              checked={formData.beilageSonstige || false}
              onChange={handleInputChange}
            />
            <div style={{ marginLeft: '8px', position: 'relative', flexGrow: 1 }}>
              <input
                name="beilageSonstigeText"
                value={formData.beilageSonstigeText}
                onChange={handleInputChange}
                maxLength={FIELD_LIMITS.beilageSonstigeText}
                style={{
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: 'none',
                  fontSize: '1rem',
                  backgroundColor: '#f3f0ff',
                  color: '#1a1a1a',
                  width: '100%'
                }}
                placeholder=""
              />
            </div>
          </label>
          <label>
            <input 
              type="checkbox" 
              name="plombenEntfernt"
              checked={formData.plombenEntfernt}
              onChange={handleInputChange}
            /> 
            Plomben wurden entfernt
          </label>
          <label>
            <input 
              type="checkbox" 
              name="verteilerEigentuemer"
              checked={formData.verteilerEigentuemer}
              onChange={handleInputChange}
            /> 
            SiNa + Zusatzdokument an Eigentümer/Verwaltung
          </label>
          <label>
            <input 
              type="checkbox" 
              name="verteilerNetz"
              checked={formData.verteilerNetz}
              onChange={handleInputChange}
            /> 
            SiNa an Netzbetreiberin/ESTI
          </label>
        </div>
      </Section>

      <Section title="Plomben, Verteiler, Schluss" icon="🔒">
        <div className="form-field">
          <label>Eingang am:</label>
          <input 
            name="eingangDatum"
            value={formData.eingangDatum}
            onChange={handleInputChange}
            maxLength={FIELD_LIMITS.eingangDatum}
          />
        </div>
        
        <div className="checkbox-group">
          <h4>Stichprobe:</h4>
          <label>
            <input 
              type="checkbox" 
              name="stichprobeJa"
              checked={formData.stichprobeJa}
              onChange={handleInputChange}
            /> 
            Ja
          </label>
          <label>
            <input 
              type="checkbox" 
              name="stichprobeNein"
              checked={formData.stichprobeNein}
              onChange={handleInputChange}
            /> 
            Nein
          </label>
          
          <h4>Befund:</h4>
          <label>
            <input 
              type="checkbox" 
              name="keineMaengel"
              checked={formData.keineMaengel}
              onChange={handleInputChange}
            /> 
            Keine Mängel festgestellt
          </label>
          <label>
            <input 
              type="checkbox" 
              name="maengelbericht"
              checked={formData.maengelbericht}
              onChange={handleInputChange}
            /> 
            Mängelbericht erstellt
          </label>
          <label>
            <input 
              type="checkbox" 
              name="anlagePlombiert"
              checked={formData.anlagePlombiert}
              onChange={handleInputChange}
            /> 
            Anlage plombiert
          </label>
          
          <div className="form-field">
            <label>Datum, Visum:</label>
            <input 
              type="date"
              name="datumVisum"
              value={formData.datumVisum}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </Section>

      {/* Generate PDF Button */}
      <div className="pdf-button-wrapper">
        <button 
          className="generate-pdf-button" 
          onClick={handleGeneratePDF}
          disabled={isLoading}
        >
          {isLoading ? '📄 PDF wird generiert...' : '📄 Generate PDF'}
        </button>
      </div>
    </div>
  );
};

export default SiNaForm;